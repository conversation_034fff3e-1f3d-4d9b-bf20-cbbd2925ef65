export interface Bid {
  id: string;
  jobId: string;
  providerId: string;
  customerId: string;
  amount: number;
  description: string;
  status: BidStatus;
  submittedAt: string;
  updatedAt: string;
  createdAt: string;
  acceptedAt?: string;
  rejectedAt?: string;
  withdrawnAt?: string;
  provider?: {
    id: string;
    firstName: string;
    lastName: string;
    businessName?: string;
    avatar?: string;
    rating: number;
    specialty: string[];
  };
  customer?: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
  };
  job?: {
    id: string;
    title: string;
    description: string;
    serviceType: string;
    location?: string;
    scheduledDate?: string;
  };
}

export type BidStatus = 'pending' | 'accepted' | 'rejected' | 'withdrawn' | 'requested';

export interface CreateBidRequest {
  jobId: string;
  amount: number;
  description: string;
  estimated_completion_time: string;
}

export interface UpdateBidRequest {
  amount?: number;
  description?: string;
}

export interface UpdateBidStatusRequest {
  status: 'accepted' | 'rejected';
}

export interface BidFilters {
  status?: BidStatus;
  jobId?: string;
  providerId?: string;
  customerId?: string;
  minAmount?: number;
  maxAmount?: number;
  dateFrom?: string;
  dateTo?: string;
  startDate?: Date;
  endDate?: Date;
  location?: string;
  category?: string;
  providerRating?: number;
}

export interface BidSortOptions {
  field: 'amount' | 'submittedAt' | 'updatedAt' | 'createdAt' | 'providerRating';
  direction: 'asc' | 'desc';
}

export interface BidListResponse {
  bids: Bid[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface BidStatistics {
  totalBids: number;
  pendingBids: number;
  acceptedBids: number;
  rejectedBids: number;
  withdrawnBids: number;
  averageBidAmount: number;
  totalBidValue: number;
  bidsByStatus: {
    pending: number;
    accepted: number;
    rejected: number;
    withdrawn: number;
  };
  bidsByMonth: {
    month: string;
    count: number;
    totalValue: number;
  }[];
}

export interface BidActionResponse {
  success: boolean;
  message: string;
  bid?: Bid;
}