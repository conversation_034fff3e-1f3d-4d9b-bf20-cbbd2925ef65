
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter
} from "@/components/ui/sidebar";
import { 
  LayoutDashboard, 
  Calendar, 
  MessageSquare, 
  User, 
  Settings, 
  ClipboardList,
  CheckSquare,
  CreditCard,
  Star,
  Shield,
  Gift
} from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuth } from "@/features/auth/hooks/useAuth";

const menuItems = [
  {
    title: "Dashboard",
    url: "/customer/dashboard",
    icon: LayoutDashboard,
    tab: undefined,
  },
  {
    title: "Active Jobs",
    url: "/customer/dashboard?tab=active-jobs",
    icon: ClipboardList,
    tab: "active-jobs",
  },
  {
    title: "Completed Jobs",
    url: "/customer/dashboard?tab=completed-jobs",
    icon: CheckSquare,
    tab: "completed-jobs",
  },
  {
    title: "Messages",
    url: "/customer/dashboard?tab=messages",
    icon: MessageSquare,
    tab: "messages",
  },
  {
    title: "Calendar",
    url: "/customer/dashboard?tab=calendar",
    icon: Calendar,
    tab: "calendar",
  },
  {
    title: "Payments",
    url: "/customer/dashboard?tab=payments",
    icon: CreditCard,
    tab: "payments",
  },
  {
    title: "Reviews",
    url: "/customer/dashboard?tab=reviews",
    icon: Star,
    tab: "reviews",
  },
  {
    title: "Rewards",
    url: "/customer/dashboard?tab=rewards",
    icon: Shield,
    tab: "rewards",
  },
  {
    title: "Referrals",
    url: "/customer/dashboard?tab=referrals",
    icon: Gift,
    tab: "referrals",
  },
  {
    title: "Profile",
    url: "/customer/dashboard?tab=profile",
    icon: User,
    tab: "profile",
  },
  {
    title: "Settings",
    url: "/customer/dashboard?tab=settings",
    icon: Settings,
    tab: "settings",
  }
];

function getTabFromSearch(search: string) {
  const params = new URLSearchParams(search);
  return params.get("tab");
}

function getInitials(name?: string) {
  if (!name) return 'U';
  return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
}

export function CustomerSidebar() {
  const location = useLocation();
  const currentTab = getTabFromSearch(location.search);
  const { user } = useAuth();

  return (
    <Sidebar>
      <SidebarHeader className="p-4 border-b">
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={user?.avatar} alt={user?.name || 'User'} />
            <AvatarFallback className="text-sm">
              {getInitials(user?.name)}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">{user?.name || 'User'}</p>
            <p className="text-xs text-muted-foreground truncate">{user?.email}</p>
          </div>
        </div>
      </SidebarHeader>
      
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Customer Dashboard</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => {
                const isDashboard =
                  item.tab === undefined &&
                  location.pathname === "/customer/dashboard" &&
                  (!location.search || !getTabFromSearch(location.search));
                const isTab =
                  item.tab &&
                  location.pathname === "/customer/dashboard" &&
                  currentTab === item.tab;

                // Special handling for Active Jobs - also active when viewing job details
                const isActiveJobsPage =
                  item.tab === "active-jobs" &&
                  (location.pathname.includes("/customer/jobs/") ||
                   location.pathname.startsWith("/customer/jobs/") ||
                   (location.pathname === "/customer/dashboard" && currentTab === "active-jobs"));

                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      isActive={Boolean(isDashboard || isTab || isActiveJobsPage)}
                    >
                      <Link to={item.url}>
                        <item.icon className="w-4 h-4" />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      
      <SidebarFooter className="p-4 border-t">
        <div className="text-xs text-muted-foreground">
          Customer Dashboard v1.0
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
