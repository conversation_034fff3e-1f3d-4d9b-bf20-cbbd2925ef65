
import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { CustomerSidebar } from './CustomerSidebar';
import { SidebarProvider, useSidebar } from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';
import { MobileCustomerHeader } from './MobileCustomerHeader';
import Footer from '@/components/Footer';
import { MobileNavigation } from '@/components/MobileNavigation';
import { MobileFooter } from '@/components/MobileFooter';

const CustomerLayout: React.FC = () => {
  const isMobile = useIsMobile();
  const location = useLocation();

  // Get dynamic title based on current route
  const getPageTitle = () => {
    if (location.pathname.includes('/jobs/') && location.pathname.includes('/manage')) {
      return 'Job Details';
    }
    if (location.pathname.includes('/jobs/') && location.pathname.includes('/bids')) {
      return 'Job Bids';
    }
    if (location.pathname.includes('?tab=active-jobs')) {
      return 'Active Jobs';
    }
    if (location.pathname.includes('?tab=completed-jobs')) {
      return 'Completed Jobs';
    }
    if (location.pathname.includes('?tab=messages')) {
      return 'Messages';
    }
    if (location.pathname.includes('?tab=calendar')) {
      return 'Calendar';
    }
    if (location.pathname.includes('?tab=payments')) {
      return 'Payments';
    }
    if (location.pathname.includes('?tab=reviews')) {
      return 'Reviews';
    }
    if (location.pathname.includes('?tab=profile')) {
      return 'Profile';
    }
    if (location.pathname.includes('?tab=settings')) {
      return 'Settings';
    }
    return 'Dashboard';
  };

  if (isMobile) {
    return (
      <div className="flex flex-col min-h-screen">
        <div className="bg-gray-50 dark:bg-gray-900 flex-1 flex flex-col">
          <MobileCustomerHeader title={getPageTitle()} />
          <div className="flex-1 overflow-auto pt-14 pb-20">
            <div className="p-4">
              <Outlet />
            </div>
          </div>
        </div>
        <MobileNavigation />
        <MobileFooter />
      </div>
    );
  }

  return (
    <SidebarProvider defaultOpen={true}>
      <DesktopCustomerLayout />
    </SidebarProvider>
  );
};

const DesktopCustomerLayout: React.FC = () => {
  const { state: sidebarState } = useSidebar();

  return (
    <div className="flex flex-col min-h-screen w-full">
      <div className="flex flex-1">
        <CustomerSidebar />
        <div
          className={cn(
            "flex-1 overflow-auto transition-all",
            sidebarState === "collapsed" ? "pl-2" : "pl-4"
          )}
        >
          <div className="w-full max-w-none pt-6 pb-[100px] px-4 lg:px-6">
            <Outlet />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}

export default CustomerLayout;
