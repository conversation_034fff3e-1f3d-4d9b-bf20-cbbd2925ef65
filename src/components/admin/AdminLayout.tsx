
import React from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { AdminSidebar } from './AdminSidebar';
import { AdminSidebarMobile } from './AdminSidebarMobile';
import { cn } from '@/lib/utils';
import Footer from '@/components/Footer';
import { MobileNavigation } from '@/components/MobileNavigation';
import { MobileFooter } from '@/components/MobileFooter';
import { UserDropdown } from './UserDropdown';

/**
 * AdminLayout Component
 *
 * This component serves as a wrapper for all admin routes, ensuring that
 * the sidebar and footer are consistently displayed across all admin pages.
 */
const AdminLayout: React.FC = () => {
  const isMobile = useIsMobile();

  return (
    <div className="flex flex-col min-h-screen">
      {/* Header with user dropdown */}
      <header className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 px-4 py-2 flex justify-end items-center h-14">
        <UserDropdown />
      </header>

      {/* Main content with sidebar */}
      <div className="flex flex-1 min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Sidebar - different implementation for mobile and desktop */}
        {!isMobile && <AdminSidebar />}

        {/* Main content area */}
        <div className={cn(
          "flex-1 transition-all pb-16",
          isMobile ? "w-full" : "ml-14" // Account for the fixed width sidebar on desktop
        )}>
          {/* Outlet renders the child route components */}
          <Outlet />
        </div>
      </div>

      {/* Footer */}
      {isMobile ? (
        <>
          <MobileNavigation />
          <MobileFooter />
        </>
      ) : (
        <Footer />
      )}
    </div>
  );
};

// Change from default export to named export
export { AdminLayout };
