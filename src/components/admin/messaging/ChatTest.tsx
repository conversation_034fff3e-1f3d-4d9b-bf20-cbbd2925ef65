import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useChat } from '@/hooks/useChat';
import { Wifi, WifiOff, MessageSquare, Users, AlertCircle } from 'lucide-react';

/**
 * Test component to verify chat functionality
 * This can be used for debugging and testing the chat implementation
 */
export const ChatTest: React.FC = () => {
  const { 
    chats, 
    currentChat, 
    messages, 
    isLoading, 
    isConnected, 
    error, 
    loadChats, 
    joinChat, 
    leaveChat,
    sendMessage,
    reconnect 
  } = useChat();

  const handleTestMessage = async () => {
    if (currentChat) {
      try {
        await sendMessage(currentChat.id, {
          type: 'text',
          message: `Test message sent at ${new Date().toLocaleTimeString()}`
        });
      } catch (error) {
        console.error('Failed to send test message:', error);
      }
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Chat System Test</h2>
        <div className="flex items-center gap-2">
          {isConnected ? (
            <Badge variant="default" className="bg-green-500">
              <Wifi className="h-3 w-3 mr-1" />
              Connected
            </Badge>
          ) : (
            <Badge variant="destructive">
              <WifiOff className="h-3 w-3 mr-1" />
              Disconnected
            </Badge>
          )}
          {!isConnected && (
            <Button onClick={reconnect} size="sm" variant="outline">
              Reconnect
            </Button>
          )}
        </div>
      </div>

      {error && (
        <Card className="p-4 border-red-200 bg-red-50">
          <div className="flex items-center gap-2 text-red-700">
            <AlertCircle className="h-4 w-4" />
            <span className="font-medium">Error:</span>
            <span>{error}</span>
          </div>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Connection Status */}
        <Card className="p-4">
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <Wifi className="h-4 w-4" />
            Connection Status
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>WebSocket:</span>
              <Badge variant={isConnected ? "default" : "destructive"}>
                {isConnected ? "Connected" : "Disconnected"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Loading:</span>
              <Badge variant={isLoading ? "secondary" : "outline"}>
                {isLoading ? "Yes" : "No"}
              </Badge>
            </div>
          </div>
        </Card>

        {/* Chats Overview */}
        <Card className="p-4">
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Chats Overview
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Total Chats:</span>
              <Badge variant="outline">{chats.length}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Current Chat:</span>
              <Badge variant={currentChat ? "default" : "secondary"}>
                {currentChat ? "Active" : "None"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Messages:</span>
              <Badge variant="outline">{messages.length}</Badge>
            </div>
          </div>
        </Card>

        {/* Actions */}
        <Card className="p-4">
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <Users className="h-4 w-4" />
            Test Actions
          </h3>
          <div className="space-y-2">
            <Button 
              onClick={loadChats} 
              disabled={isLoading}
              className="w-full"
              size="sm"
            >
              Load Chats
            </Button>
            <Button 
              onClick={handleTestMessage}
              disabled={!currentChat || isLoading}
              className="w-full"
              size="sm"
              variant="outline"
            >
              Send Test Message
            </Button>
            <Button 
              onClick={leaveChat}
              disabled={!currentChat}
              className="w-full"
              size="sm"
              variant="secondary"
            >
              Leave Current Chat
            </Button>
          </div>
        </Card>
      </div>

      {/* Chat List */}
      {chats.length > 0 && (
        <Card className="p-4">
          <h3 className="font-semibold mb-3">Available Chats</h3>
          <div className="space-y-2">
            {chats.map((chat) => {
              const otherParticipant = chat.participants.find(p => p.type !== 'admin');
              return (
                <div 
                  key={chat.id}
                  className={`p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                    currentChat?.id === chat.id ? 'border-blue-500 bg-blue-50' : ''
                  }`}
                  onClick={() => joinChat(chat.id)}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="font-medium">
                        {otherParticipant?.name || 'Unknown User'}
                      </div>
                      <div className="text-sm text-gray-500">
                        {chat.last_message?.message || 'No messages'}
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant="outline" className="text-xs">
                        {otherParticipant?.type || 'unknown'}
                      </Badge>
                      {(chat.unread_count || 0) > 0 && (
                        <div className="text-xs text-blue-600 mt-1">
                          {chat.unread_count} unread
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </Card>
      )}

      {/* Current Chat Messages */}
      {currentChat && messages.length > 0 && (
        <Card className="p-4">
          <h3 className="font-semibold mb-3">
            Messages in {currentChat.participants.find(p => p.type !== 'admin')?.name || 'Current Chat'}
          </h3>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {messages.map((message) => (
              <div 
                key={message.id}
                className={`p-2 rounded ${
                  message.user.type === 'admin' 
                    ? 'bg-blue-100 ml-8' 
                    : 'bg-gray-100 mr-8'
                }`}
              >
                <div className="text-xs text-gray-500 mb-1">
                  {message.user.name} • {new Date(message.created_at).toLocaleTimeString()}
                </div>
                <div className="text-sm">{message.message}</div>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};
