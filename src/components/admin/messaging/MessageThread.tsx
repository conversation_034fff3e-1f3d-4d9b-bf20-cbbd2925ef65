
import { useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useChat } from "@/hooks/useChat";
import { useAuth } from "@/features/auth/hooks/useAuth";

interface MessageThreadProps {
  chatId: string;
  messageEndRef: React.RefObject<HTMLDivElement>;
}

export const MessageThread = ({
  chatId,
  messageEndRef
}: MessageThreadProps) => {
  const { messages, loadMessages, currentChat } = useChat();
  const { user } = useAuth();
  const currentUserId = user?.id;

  useEffect(() => {
    if (chatId) {
      loadMessages(chatId);
    }
  }, [chatId, loadMessages]);

  // Format timestamp for display
  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    
    const isToday = date.toDateString() === now.toDateString();
    const isYesterday = date.toDateString() === yesterday.toDateString();
    
    if (isToday) {
      return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (isYesterday) {
      return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <ScrollArea className="h-[calc(100%-80px)]">
      {messages.length > 0 ? (
        <div className="space-y-4 p-4">
          {messages.map((message) => {
            const isFromCurrentUser = message.user_id === currentUserId;

            return (
              <div
                key={message.id}
                className={`flex ${
                  isFromCurrentUser ? "justify-end" : "justify-start"
                }`}
              >
                {!isFromCurrentUser && (
                  <Avatar className="h-8 w-8 mr-2">
                    <AvatarImage src={message.user.avatar} />
                    <AvatarFallback>{getInitials(message.user.name)}</AvatarFallback>
                  </Avatar>
                )}
                <div
                  className={`max-w-[80%] rounded-lg p-3 ${
                    isFromCurrentUser
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted"
                  }`}
                >
                  <p className="text-sm whitespace-pre-wrap break-words">{message.message}</p>
                  <div className="text-xs mt-1 opacity-70 text-right">
                    {formatMessageTime(message.created_at)}
                    {isFromCurrentUser && message.read_at && (
                      <span className="ml-2">✓✓</span>
                    )}
                    {isFromCurrentUser && !message.read_at && (
                      <span className="ml-2">✓</span>
                    )}
                  </div>
                </div>
                {isFromCurrentUser && (
                  <Avatar className="h-8 w-8 ml-2">
                    <AvatarImage src={message.user.avatar} />
                    <AvatarFallback>{getInitials(message.user.name)}</AvatarFallback>
                  </Avatar>
                )}
              </div>
            );
          })}
          <div ref={messageEndRef} />
        </div>
      ) : (
        <div className="flex items-center justify-center h-full">
          <p className="text-muted-foreground">No messages yet</p>
        </div>
      )}
    </ScrollArea>
  );
};
