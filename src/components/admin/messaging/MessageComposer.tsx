
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Paperclip, Send } from "lucide-react";
import { useChat } from "@/hooks/useChat";
import { useToast } from "@/hooks/use-toast";

interface MessageComposerProps {
  chatId: string;
  recipientName: string;
}

export const MessageComposer = ({ chatId, recipientName }: MessageComposerProps) => {
  const [message, setMessage] = useState("");
  const [isSending, setIsSending] = useState(false);
  const { sendMessage } = useChat();
  const { toast } = useToast();

  const handleSendMessage = async () => {
    if (!message.trim()) return;

    setIsSending(true);
    try {
      await sendMessage(chatId, {
        type: 'text',
        message: message.trim()
      });

      // Clear input
      setMessage("");

      toast({
        title: "Message sent",
        description: `Your message has been sent to ${recipientName}.`,
      });
    } catch (error) {
      console.error("Failed to send message:", error);
      toast({
        title: "Failed to send message",
        description: "Please try again later.",
        variant: "destructive"
      });
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="border rounded-md p-3 bg-background">
      <Textarea
        placeholder={`Message ${recipientName}...`}
        className="min-h-[80px] resize-none border-0 p-2 focus-visible:ring-0 focus-visible:ring-offset-0"
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyDown={handleKeyDown}
        disabled={isSending}
      />
      <div className="flex justify-between items-center mt-2">
        <Button variant="ghost" size="icon" disabled={isSending}>
          <Paperclip className="h-4 w-4" />
        </Button>
        <Button
          onClick={handleSendMessage}
          disabled={!message.trim() || isSending}
          className="px-3"
        >
          <Send className="h-4 w-4 mr-2" />
          {isSending ? "Sending..." : "Send"}
        </Button>
      </div>
    </div>
  );
};
