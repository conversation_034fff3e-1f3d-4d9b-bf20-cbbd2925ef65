
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/features/auth/hooks/useAuth';
import {
  LayoutDashboard,
  Users,
  Briefcase,
  Settings,
  MessageSquare,
  UserCircle,
  CreditCard,
  Star,
  Gift,
  Share2,
  Mail,
  LogOut,
  Calendar,
  Building,
  Activity,
  ShieldAlert,
  ShieldCheck,
  FileText
} from "lucide-react";
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

// Define the navigation items to display
const navigationItems = [
  {
    name: "Dashboard",
    path: "/admin",
    icon: LayoutDashboard
  },
  {
    name: "Manage Job Booking",
    path: "/admin/bookings",
    icon: Calendar
  },
  {
    name: "Job Oversight",
    path: "/admin/job-oversight",
    icon: <PERSON><PERSON><PERSON><PERSON>,
    badge: "New"
  },
  {
    name: "Manage Business",
    path: "/admin/business",
    icon: Building
  },
  {
    name: "Providers",
    path: "/admin/providers",
    icon: Users
  },
  {
    name: "Provider Invitations",
    path: "/admin/provider-invitations",
    icon: Mail
  },
  {
    name: "Customers",
    path: "/admin/customers",
    icon: UserCircle
  },
  {
    name: "Jobs",
    path: "/admin/jobs",
    icon: Briefcase
  },
  {
    name: "Bids Dashboard",
    path: "/admin/dashboard/bids",
    icon: FileText
  },
  {
    name: "Payments",
    path: "/admin/payments",
    icon: CreditCard
  },
  {
    name: "Messages",
    path: "/admin/messages",
    icon: MessageSquare,
    badge: 3
  },
  {
    name: "Reviews",
    path: "/admin/reviews",
    icon: Star
  },
  {
    name: "Rewards",
    path: "/admin/rewards",
    icon: Gift
  },
  {
    name: "Referrals",
    path: "/admin/referrals",
    icon: Share2
  },
  {
    name: "Settings",
    path: "/admin/settings",
    icon: Settings
  },
  {
    name: "Manage Certificates",
    path: "/admin/certificates",
    icon: ShieldCheck
  },
];

interface AdminSidebarMobileProps {
  onNavigate?: () => void;
}

export const AdminSidebarMobile = ({ onNavigate }: AdminSidebarMobileProps) => {
  const location = useLocation();
  const path = location.pathname;
  const { toast } = useToast();
  const { logout } = useAuth();

  const handleLogout = () => {
    logout(); // Handle logout
    toast({
      title: "Logged out successfully",
      description: "You have been logged out of your account."
    });

    // Close the sidebar if onNavigate is provided
    if (onNavigate) {
      onNavigate();
    }
  };

  const handleNavigation = () => {
    // Call the onNavigate callback if provided
    if (onNavigate) {
      onNavigate();
    }
  };

  return (
    <div className="flex flex-col h-full w-full">
      <ScrollArea className="flex-1 overflow-y-auto pb-4">
        <div className="flex flex-col w-full space-y-1 p-1">
          {navigationItems.map(item => (
            <Link
              key={item.path}
              to={item.path}
              className={cn(
                "flex items-center px-4 py-3 rounded-md transition-colors relative",
                path === item.path 
                  ? "bg-primary/10 text-primary" 
                  : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
              )}
              onClick={handleNavigation}
            >
              <item.icon className="h-5 w-5 mr-3" />
              <span>{item.name}</span>
              {item.badge && (
                typeof item.badge === 'number' ? (
                  <Badge 
                    variant="destructive" 
                    className="ml-auto"
                  >
                    {item.badge}
                  </Badge>
                ) : (
                  <Badge 
                    variant="outline" 
                    className="ml-auto bg-purple-100 text-purple-800 border-purple-200"
                  >
                    {item.badge}
                  </Badge>
                )
              )}
            </Link>
          ))}
        </div>
      </ScrollArea>

      <div className="mt-auto px-1 py-2">
        <Separator className="mb-2" />
        <Button 
          variant="ghost" 
          className="flex items-center justify-start text-left text-red-500 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-900/10 px-4 py-3 w-full"
          onClick={handleLogout}
        >
          <LogOut className="h-5 w-5 mr-3" />
          <span>Log Out</span>
        </Button>
      </div>
    </div>
  );
};
