import { z } from "zod";

// Provider Tier Management Form Schema
export const providerTierManagementSchema = z.object({
  providerId: z.string().min(1, "Provider is required"),
  planId: z.string().min(1, "Subscription plan is required"),
  duration: z.enum(["monthly", "yearly"]).optional(),
  notes: z.string().max(500, "Notes cannot exceed 500 characters").optional(),
});

export type ProviderTierManagementFormValues = z.infer<typeof providerTierManagementSchema>;

// Provider Plan Schema
export const providerPlanSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Plan name is required"),
  price: z.number().min(0, "Price must be a positive number"),
  description: z.string().min(1, "Description is required"),
  commission: z.string().min(1, "Commission rate is required"),
  features: z.array(
    z.object({
      included: z.boolean(),
      text: z.string().min(1, "Feature description is required"),
    })
  ).min(1, "At least one feature is required"),
});

export type ProviderPlan = z.infer<typeof providerPlanSchema>;