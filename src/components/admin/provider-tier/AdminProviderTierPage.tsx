import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ProviderTierManagementForm } from "./ProviderTierManagementForm";
import { PlanManagementForm } from "./PlanManagementForm";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { ProviderPlan } from "./schemas";
import { Pencil, Trash2, Loader2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { planService } from "@/services/planService";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { nullToUndefined } from "@/utils/typeHelpers";

export const AdminProviderTierPage = () => {
  const { toast } = useToast();
  const { token } = useAuth();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingPlan, setEditingPlan] = useState<ProviderPlan | null>(null);
  const [plans, setPlans] = useState<ProviderPlan[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  // Fetch plans when component mounts
  useEffect(() => {
    const fetchPlans = async () => {
      setIsLoading(true);
      try {
        const response = await planService.getPlans(1, 100, nullToUndefined(token));
        if (response.success && response.data) {
          setPlans(response.data.data);
        } else {
          toast({
            title: "Error fetching plans",
            description: response.message || "Failed to load subscription plans",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error fetching plans:", error);
        toast({
          title: "Error",
          description: "Failed to load plans. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchPlans();
  }, [token, toast]);

  const handlePlanSubmit = async (plan: ProviderPlan) => {
    try {
      let response;
      
      if (editingPlan) {
        // Update existing plan
        response = await planService.updatePlan({
          ...plan,
          id: editingPlan.id || "",
        }, nullToUndefined(token));

        if (response.success) {
          setPlans(plans.map(p => p.id === editingPlan.id ? plan : p));
          toast({
            title: "Plan updated",
            description: `${plan.name} plan has been updated successfully.`,
          });
        } else {
          toast({
            title: "Update failed",
            description: response.message || "Failed to update plan",
            variant: "destructive",
          });
          return; // Don't close dialog if update failed
        }
      } else {
        // Add new plan
        response = await planService.createPlan(plan, nullToUndefined(token));
        
        if (response.success && response.data) {
          setPlans([...plans, response.data]);
          toast({
            title: "Plan created",
            description: `${plan.name} plan has been created successfully.`,
          });
        } else {
          toast({
            title: "Creation failed",
            description: response.message || "Failed to create plan",
            variant: "destructive",
          });
          return; // Don't close dialog if creation failed
        }
      }
      
      setIsDialogOpen(false);
      setEditingPlan(null);
    } catch (error) {
      console.error("Error saving plan:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeletePlan = async (planId: string) => {
    try {
      setIsDeleting(planId);
      const response = await planService.deletePlan(planId, nullToUndefined(token));
      
      if (response.success) {
        setPlans(plans.filter(p => p.id !== planId));
        toast({
          title: "Plan deleted",
          description: "The subscription plan has been deleted successfully.",
        });
      } else {
        toast({
          title: "Deletion failed",
          description: response.message || "Failed to delete plan",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error deleting plan:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred while deleting the plan.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(null);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Provider Tier Management</h1>
      </div>

      <Tabs defaultValue="assign">
          <TabsList className="mb-6">
            <TabsTrigger value="assign">Assign Plans</TabsTrigger>
            <TabsTrigger value="manage">Manage Plans</TabsTrigger>
          </TabsList>

          <TabsContent value="assign">
            <Card>
              <CardHeader>
                <CardTitle>Assign Plan to Provider</CardTitle>
                <CardDescription>
                  Assign subscription plans to service providers and manage their tier status.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ProviderTierManagementForm />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="manage">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Subscription Plans</h2>
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button onClick={() => setEditingPlan(null)}>
                    Add New Plan
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>
                      {editingPlan ? "Edit Plan" : "Create New Plan"}
                    </DialogTitle>
                  </DialogHeader>
                  <PlanManagementForm 
                    initialData={editingPlan || undefined} 
                    onSubmit={handlePlanSubmit} 
                  />
                </DialogContent>
              </Dialog>
            </div>

            {isLoading ? (
              <div className="flex items-center justify-center p-12">
                <Loader2 className="h-8 w-8 animate-spin mr-2" />
                <span>Loading subscription plans...</span>
              </div>
            ) : plans.length === 0 ? (
              <div className="text-center p-12">
                <p className="text-gray-500 mb-4">No subscription plans found</p>
                <Button onClick={() => {
                  setEditingPlan(null);
                  setIsDialogOpen(true);
                }}>
                  Create your first plan
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {plans.map((plan) => (
                  <Card key={plan.id} className="relative overflow-hidden">
                    {plan.price === 0 && (
                      <Badge className="absolute top-2 right-2 bg-green-500 hover:bg-green-600">
                        Free
                      </Badge>
                    )}
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle>{plan.name}</CardTitle>
                          <CardDescription className="mt-1">
                            ${plan.price}{plan.price === 0 ? '' : '/month'} - {plan.commission} commission
                          </CardDescription>
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => {
                              setEditingPlan(plan);
                              setIsDialogOpen(true);
                            }}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            className="text-destructive hover:text-destructive"
                            onClick={() => handleDeletePlan(plan.id || "")}
                            disabled={isDeleting === plan.id}
                          >
                            {isDeleting === plan.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 mb-4">{plan.description}</p>
                      <div className="space-y-2">
                        {plan.features.map((feature, index) => (
                          <div key={index} className="flex items-start">
                            <div className={`mr-2 mt-0.5 ${feature.included ? 'text-green-500' : 'text-gray-400'}`}>
                              {feature.included ? (
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                              ) : (
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <line x1="18" y1="6" x2="6" y2="18"></line>
                                  <line x1="6" y1="6" x2="18" y2="18"></line>
                                </svg>
                              )}
                            </div>
                            <span className={`text-sm ${feature.included ? 'text-gray-700' : 'text-gray-500'}`}>
                              {feature.text}
                            </span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
    </div>
  );
};