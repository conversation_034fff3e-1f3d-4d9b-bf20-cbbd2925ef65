import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/hooks/use-toast";
import { ProviderPlan, providerPlanSchema } from "./schemas";
import { Plus, Trash2, X } from "lucide-react";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Loader2 } from "lucide-react";

interface PlanManagementFormProps {
  initialData?: ProviderPlan;
  onSubmit: (plan: ProviderPlan) => Promise<void>;
}

export const PlanManagementForm = ({
  initialData,
  onSubmit,
}: PlanManagementFormProps) => {
  const { toast } = useToast();
  const [features, setFeatures] = useState<{ included: boolean; text: string }[]>(
    initialData?.features || [{ included: true, text: "" }]
  );

  const form = useForm<ProviderPlan>({
    resolver: zodResolver(providerPlanSchema),
    defaultValues: initialData || {
      id: "",
      name: "",
      price: 0,
      description: "",
      commission: "",
      features: [],
    },
  });

  const { formState: { isSubmitting } } = form;

  // Update form value when features change
  useEffect(() => {
    form.setValue("features", features.filter(feature => feature.text.trim() !== ""));
  }, [features, form]);

  const addFeature = () => {
    setFeatures([...features, { included: true, text: "" }]);
  };

  const removeFeature = (index: number) => {
    const newFeatures = [...features];
    newFeatures.splice(index, 1);
    setFeatures(newFeatures);
  };

  const updateFeature = (index: number, field: "included" | "text", value: boolean | string) => {
    const newFeatures = [...features];
    if (field === "included") {
      newFeatures[index].included = value as boolean;
    } else {
      newFeatures[index].text = value as string;
    }
    setFeatures(newFeatures);
  };

  const onSubmitForm = async (data: ProviderPlan) => {
    try {
      // Filter out empty features
      const filteredFeatures = features.filter(feature => feature.text.trim() !== "");
      const planData = {
        ...data,
        features: filteredFeatures,
      };
      
      // Call the provided onSubmit function with the plan data
      await onSubmit(planData);
    } catch (error) {
      console.error("Error saving plan:", error);
      toast({
        title: "Error",
        description: "There was an error saving the plan. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{initialData ? "Edit" : "Create"} Subscription Plan</CardTitle>
        <CardDescription>
          {initialData 
            ? "Update the details of an existing subscription plan." 
            : "Create a new subscription plan for providers."}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmitForm)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Plan Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Pro Plan" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Monthly Price ($)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="0" 
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="commission"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Commission Rate</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., 15%" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Brief description of the plan"
                      className="resize-none"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <FormLabel>Features</FormLabel>
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm" 
                  onClick={addFeature}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Feature
                </Button>
              </div>
              
              {features.map((feature, index) => (
                <div key={index} className="flex items-start space-x-2">
                  <div className="pt-2">
                    <Switch 
                      checked={feature.included} 
                      onCheckedChange={(checked) => updateFeature(index, "included", checked)}
                    />
                  </div>
                  <div className="flex-1">
                    <Input 
                      value={feature.text} 
                      onChange={(e) => updateFeature(index, "text", e.target.value)}
                      placeholder="Feature description"
                    />
                  </div>
                  <Button 
                    type="button" 
                    variant="ghost" 
                    size="icon"
                    onClick={() => removeFeature(index)}
                    disabled={features.length === 1}
                  >
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                </div>
              ))}
            </div>

            <div className="flex justify-end pt-4">
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {initialData ? "Updating..." : "Creating..."}
                  </>
                ) : (
                  initialData ? "Update Plan" : "Create Plan"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};