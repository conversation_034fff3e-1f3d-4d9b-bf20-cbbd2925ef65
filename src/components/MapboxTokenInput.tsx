
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { saveMapboxToken } from '@/lib/mapbox-client';
import { AlertCircle } from 'lucide-react';

export const MapboxTokenInput = () => {
  const [token, setToken] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!token.trim()) return;
    
    setIsSubmitting(true);
    saveMapboxToken(token.trim());
    // The page will reload via the saveMapboxToken function
  };

  return (
    <div className="flex items-center justify-center min-h-[50vh] p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Mapbox Token Required</CardTitle>
          <CardDescription>
            To use address features, please provide your Mapbox public access token.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                You can get your token by signing up at <a href="https://www.mapbox.com/" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">mapbox.com</a> and creating a public token.
              </p>
              
              <div className="flex items-start mb-4">
                <AlertCircle className="text-yellow-500 mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
                <p className="text-sm text-muted-foreground">
                  This token will be stored locally in your browser and is only used for address lookup features.
                </p>
              </div>
              
              <Input
                type="text"
                placeholder="Enter your Mapbox public token"
                value={token}
                onChange={(e) => setToken(e.target.value)}
                className="w-full"
              />
            </div>
            
            <Button type="submit" className="w-full" disabled={!token.trim() || isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Save Token'}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <p className="text-xs text-muted-foreground">
            This is a one-time setup for address autocomplete functionality.
          </p>
        </CardFooter>
      </Card>
    </div>
  );
};
