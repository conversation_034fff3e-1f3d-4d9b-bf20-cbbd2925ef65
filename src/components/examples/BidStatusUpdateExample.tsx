import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useBidStatusUpdate } from '@/hooks/useBidStatusUpdate';
import { Bid } from '@/types/bid';
import { Check, X, Loader2 } from 'lucide-react';

interface BidStatusUpdateExampleProps {
  bid: Bid;
  onBidUpdated?: (updatedBid: Bid) => void;
}

/**
 * Example component demonstrating the usage of the new updateBidStatus functionality
 * This implements Task 22: Consumer Update Bid Status (Accept/Reject)
 * 
 * Features:
 * - Uses the new unified PATCH /api/bids/{bid_id}/status endpoint
 * - Proper authentication and authorization (customer role required)
 * - Comprehensive error handling and loading states
 * - Follows the BID_API_DOCUMENTATION.md specification
 */
export const BidStatusUpdateExample: React.FC<BidStatusUpdateExampleProps> = ({
  bid,
  onBidUpdated
}) => {
  const { updateStatus, isLoading, error, clearError } = useBidStatusUpdate({
    onSuccess: (updatedBid) => {
      console.log('Bid status updated successfully:', updatedBid);
      onBidUpdated?.(updatedBid);
    },
    onError: (error) => {
      console.error('Failed to update bid status:', error);
    }
  });

  const handleAcceptBid = async () => {
    clearError(); // Clear any previous errors
    await updateStatus(bid.id, 'accepted');
  };

  const handleRejectBid = async () => {
    clearError(); // Clear any previous errors
    await updateStatus(bid.id, 'rejected');
  };

  const getBidStatusColor = (status: string): string => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'requested': return 'bg-blue-100 text-blue-800';
      case 'accepted': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'withdrawn': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const canUpdateStatus = bid.status === 'pending' || bid.status === 'requested';

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Bid Status Update Example</span>
          <Badge className={getBidStatusColor(bid.status)}>
            {bid.status.charAt(0).toUpperCase() + bid.status.slice(1)}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Bid Information */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">Bid ID:</span>
            <p className="text-gray-600 font-mono text-xs">{bid.id}</p>
          </div>
          <div>
            <span className="font-medium">Amount:</span>
            <p className="text-green-600 font-semibold">{formatCurrency(bid.amount)}</p>
          </div>
          <div>
            <span className="font-medium">Provider:</span>
            <p className="text-gray-600">
              {bid.provider?.firstName} {bid.provider?.lastName}
            </p>
          </div>
          <div>
            <span className="font-medium">Submitted:</span>
            <p className="text-gray-600">
              {new Date(bid.submittedAt).toLocaleDateString()}
            </p>
          </div>
        </div>

        {/* Bid Description */}
        <div>
          <span className="font-medium">Description:</span>
          <p className="text-gray-700 text-sm mt-1 p-2 bg-gray-50 rounded">
            {bid.description}
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-800 text-sm">{error}</p>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="mt-1 text-red-600 hover:text-red-700"
            >
              Dismiss
            </Button>
          </div>
        )}

        {/* Action Buttons */}
        {canUpdateStatus ? (
          <div className="flex gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={handleRejectBid}
              disabled={isLoading}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <X className="h-4 w-4 mr-2" />
              )}
              Reject Bid
            </Button>
            
            <Button
              onClick={handleAcceptBid}
              disabled={isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Check className="h-4 w-4 mr-2" />
              )}
              Accept Bid
            </Button>
          </div>
        ) : (
          <div className="pt-4 border-t">
            <p className="text-gray-500 text-sm">
              This bid cannot be updated (status: {bid.status})
            </p>
          </div>
        )}

        {/* Implementation Notes */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h4 className="font-medium text-blue-900 mb-2">Implementation Notes:</h4>
          <ul className="text-blue-800 text-sm space-y-1">
            <li>• Uses PATCH /api/bids/{`{bid_id}`}/status endpoint</li>
            <li>• Requires customer role authentication</li>
            <li>• Validates bid ID format (UUID)</li>
            <li>• Handles loading states and error messages</li>
            <li>• Follows BID_API_DOCUMENTATION.md specification</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};
