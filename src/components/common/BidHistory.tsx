import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';

import { useToast } from '@/hooks/use-toast';
import { getProviderBids, getJobBids } from '@/services/bidService';
import { Bid, BidStatus, BidFilters } from '@/types/bid';
import { useAuth } from '@/features/auth/hooks/useAuth';
import {
  Clock,
  DollarSign,
  User,
  MapPin,
  Calendar,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  ExternalLink,
  Loader2,
  RefreshCw,

  TrendingUp,
  TrendingDown,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { format, formatDistanceToNow } from 'date-fns';

interface BidHistoryProps {
  userRole: 'customer' | 'provider' | 'admin';
  userId?: string;
  jobId?: string;
  providerId?: string;
  onBidSelect?: (bid: Bid) => void;
  showFilters?: boolean;
  maxHeight?: string;
  className?: string;
}

interface HistoryFilters {
  status?: BidStatus;
  timeRange?: '7d' | '30d' | '90d' | 'all';
  sortBy?: 'date' | 'amount' | 'status';
  sortOrder?: 'asc' | 'desc';
}

const STATUS_CONFIG = {
  pending: {
    icon: Clock,
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    label: 'Pending'
  },
  accepted: {
    icon: CheckCircle,
    color: 'bg-green-100 text-green-800 border-green-200',
    label: 'Accepted'
  },
  rejected: {
    icon: XCircle,
    color: 'bg-red-100 text-red-800 border-red-200',
    label: 'Rejected'
  },
  withdrawn: {
    icon: AlertCircle,
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    label: 'Withdrawn'
  }
};

const TIME_RANGES = [
  { value: '7d', label: 'Last 7 days' },
  { value: '30d', label: 'Last 30 days' },
  { value: '90d', label: 'Last 90 days' },
  { value: 'all', label: 'All time' }
];

export const BidHistory: React.FC<BidHistoryProps> = ({
  userRole,
  userId,
  jobId,
  providerId,
  onBidSelect,
  showFilters = true,
  maxHeight = '600px',
  className
}) => {
  const [bids, setBids] = useState<Bid[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filters, setFilters] = useState<HistoryFilters>({
    timeRange: '30d',
    sortBy: 'date',
    sortOrder: 'desc'
  });
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const { toast } = useToast();
  const { user: currentUser, token } = useAuth();
  const effectiveUserId = userId || currentUser?.id;

  const fetchBids = async (pageNum = 1, showRefreshLoader = false) => {
    if (!effectiveUserId && !jobId && !providerId) return;
    
    if (showRefreshLoader) {
      setRefreshing(true);
    } else if (pageNum === 1) {
      setLoading(true);
    }

    try {
      let response;
      const sortOptions = {
        field: filters.sortBy === 'date' ? 'createdAt' : filters.sortBy as any,
        direction: filters.sortOrder || 'desc'
      };

      const bidFilters: Partial<BidFilters> = {};
      if (filters.status) {
        bidFilters.status = filters.status;
      }

      // Add time range filter
      if (filters.timeRange && filters.timeRange !== 'all') {
        const now = new Date();
        const daysAgo = parseInt(filters.timeRange.replace('d', ''));
        const startDate = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);
        bidFilters.startDate = startDate;
      }

      if (jobId) {
        // Get bids for a specific job
        response = await getJobBids(jobId, bidFilters, sortOptions, pageNum, 20, token || undefined);
      } else if (providerId || (userRole === 'provider' && effectiveUserId)) {
        // Get bids for a specific provider
        const targetProviderId = providerId || effectiveUserId;
        response = await getProviderBids(targetProviderId!, bidFilters, sortOptions, pageNum, 20, token || undefined);
      } else {
        // For admin or customer viewing all bids
        // This would need a different API endpoint
        response = { isSuccess: false, data: null };
      }

      if (response.isSuccess && response.data) {
        const newBids = response.data.bids || [];
        
        if (pageNum === 1) {
          setBids(newBids);
        } else {
          setBids(prev => [...prev, ...newBids]);
        }
        
        setHasMore(newBids.length === 20);
      } else {
        if (pageNum === 1) {
          setBids([]);
        }
        setHasMore(false);
      }
    } catch (error) {
      console.error('Failed to fetch bid history:', error);
      toast({
        title: 'Error',
        description: 'Failed to load bid history',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    setPage(1);
    fetchBids(1);
  }, [filters, effectiveUserId, jobId, providerId]);

  const handleFilterChange = (key: keyof HistoryFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const loadMore = () => {
    const nextPage = page + 1;
    setPage(nextPage);
    fetchBids(nextPage);
  };

  const refresh = () => {
    setPage(1);
    fetchBids(1, true);
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getStatusIcon = (status: BidStatus) => {
    const config = STATUS_CONFIG[status];
    const IconComponent = config.icon;
    return <IconComponent className="h-4 w-4" />;
  };

  const getStatusBadge = (status: BidStatus) => {
    const config = STATUS_CONFIG[status];
    return (
      <Badge variant="outline" className={config.color}>
        {getStatusIcon(status)}
        <span className="ml-1">{config.label}</span>
      </Badge>
    );
  };

  const getBidTrend = (bid: Bid): 'up' | 'down' | 'neutral' => {
    // This would typically compare with previous bids for the same job
    // For now, we'll simulate based on amount
    if (bid.amount > 1000) return 'up';
    if (bid.amount < 500) return 'down';
    return 'neutral';
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'neutral') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return null;
    }
  };

  const getTimeAgo = (date: string): string => {
    return formatDistanceToNow(new Date(date), { addSuffix: true });
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Bid History
            {bids.length > 0 && (
              <Badge variant="secondary">{bids.length} bids</Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={refresh}
              disabled={refreshing}
            >
              {refreshing ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
        
        {showFilters && (
          <div className="flex flex-wrap gap-3 mt-4">
            <Select
              value={filters.status || 'all'}
              onValueChange={(value) => handleFilterChange('status', value === 'all' ? undefined : value)}
            >
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="accepted">Accepted</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
                <SelectItem value="withdrawn">Withdrawn</SelectItem>
              </SelectContent>
            </Select>
            
            <Select
              value={filters.timeRange}
              onValueChange={(value: any) => handleFilterChange('timeRange', value)}
            >
              <SelectTrigger className="w-36">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {TIME_RANGES.map((range) => (
                  <SelectItem key={range.value} value={range.value}>
                    {range.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select
              value={`${filters.sortBy}-${filters.sortOrder}`}
              onValueChange={(value) => {
                const [sortBy, sortOrder] = value.split('-');
                handleFilterChange('sortBy', sortBy);
                handleFilterChange('sortOrder', sortOrder);
              }}
            >
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date-desc">Newest First</SelectItem>
                <SelectItem value="date-asc">Oldest First</SelectItem>
                <SelectItem value="amount-desc">Highest Amount</SelectItem>
                <SelectItem value="amount-asc">Lowest Amount</SelectItem>
                <SelectItem value="status-asc">Status A-Z</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
      </CardHeader>
      
      <CardContent className="p-0">
        <ScrollArea className={`${maxHeight}`}>
          {bids.length === 0 ? (
            <div className="text-center py-12 px-6">
              <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No bid history</h3>
              <p className="text-gray-500">
                {filters.status || filters.timeRange !== 'all'
                  ? 'No bids match your current filters'
                  : 'No bids have been submitted yet'}
              </p>
            </div>
          ) : (
            <div className="space-y-1">
              {bids.map((bid) => {
                const trend = getBidTrend(bid);
                
                return (
                  <div
                    key={bid.id}
                    className="p-4 hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0"
                  >
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1 min-w-0">
                        {/* Header */}
                        <div className="flex items-center gap-3 mb-2">
                          {getStatusBadge(bid.status)}
                          <span className="text-sm text-gray-500">
                            {getTimeAgo(bid.createdAt)}
                          </span>
                          {trend !== 'neutral' && getTrendIcon(trend)}
                        </div>
                        
                        {/* Job Info */}
                        {bid.job && (
                          <div className="mb-2">
                            <h4 className="font-medium text-gray-900 truncate">
                              {bid.job.title}
                            </h4>
                            {bid.job.location && (
                              <p className="text-sm text-gray-600 flex items-center gap-1 mt-1">
                                <MapPin className="h-3 w-3" />
                                {bid.job.location}
                              </p>
                            )}
                          </div>
                        )}
                        
                        {/* Provider Info (for customers/admin) */}
                        {bid.provider && userRole !== 'provider' && (
                          <div className="flex items-center gap-2 mb-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={bid.provider.avatar} />
                              <AvatarFallback>
                                <User className="h-3 w-3" />
                              </AvatarFallback>
                            </Avatar>
                            <span className="text-sm text-gray-700">
                              {bid.provider.firstName} {bid.provider.lastName}
                            </span>
                            {bid.provider.rating && (
                              <span className="text-sm text-yellow-600">
                                ★ {bid.provider.rating.toFixed(1)}
                              </span>
                            )}
                          </div>
                        )}
                        
                        {/* Bid Details */}
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-4 w-4" />
                            <span className="font-medium text-gray-900">
                              {formatCurrency(bid.amount)}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            <span>{format(new Date(bid.createdAt), 'MMM d, yyyy')}</span>
                          </div>
                        </div>
                        
                        {/* Description */}
                        {bid.description && (
                          <p className="text-sm text-gray-600 mt-2 line-clamp-2">
                            {bid.description}
                          </p>
                        )}
                      </div>
                      
                      {/* Actions */}
                      <div className="flex items-center gap-2">
                        {onBidSelect && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onBidSelect(bid)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        )}
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {onBidSelect && (
                              <DropdownMenuItem onClick={() => onBidSelect(bid)}>
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </DropdownMenuItem>
                            )}
                            {bid.job && (
                              <DropdownMenuItem>
                                <ExternalLink className="h-4 w-4 mr-2" />
                                View Job
                              </DropdownMenuItem>
                            )}
                            {bid.provider && userRole !== 'provider' && (
                              <DropdownMenuItem>
                                <User className="h-4 w-4 mr-2" />
                                View Provider
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </div>
                );
              })}
              
              {/* Load More */}
              {hasMore && (
                <div className="p-4 text-center border-t border-gray-100">
                  <Button
                    variant="outline"
                    onClick={loadMore}
                    disabled={loading}
                  >
                    {loading ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : null}
                    Load More
                  </Button>
                </div>
              )}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
};