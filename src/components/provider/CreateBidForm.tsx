import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { createBid } from '@/services/bidService';
import { CreateBidRequest } from '@/types/bid';
import { Job } from '@/types/jobs';
import { validateBidData, BidValidationError, getFieldError } from '@/utils/bidValidation';
import { AlertCircle, DollarSign, FileText, Calendar } from 'lucide-react';

interface CreateBidFormProps {
  job: Job;
  onBidCreated?: () => void;
  onCancel?: () => void;
}

export const CreateBidForm: React.FC<CreateBidFormProps> = ({
  job,
  onBidCreated,
  onCancel
}) => {
  const [formData, setFormData] = useState<CreateBidRequest>({
    jobId: job.id,
    amount: 0,
    description: '',
    estimated_completion_time: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<BidValidationError[]>([]);
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const { toast } = useToast();

  const handleInputChange = (field: keyof CreateBidRequest, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [field]: true
    }));

    // Validate on change for real-time feedback
    const updatedData = { ...formData, [field]: value };
    const validation = validateBidData(updatedData);
    setValidationErrors(validation.errors);
  };

  const handleBlur = (field: keyof CreateBidRequest) => {
    setTouched(prev => ({
      ...prev,
      [field]: true
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Mark all fields as touched for validation display
    setTouched({
      jobId: true,
      amount: true,
      description: true,
      estimated_completion_time: true
    });

    // Comprehensive validation
    const validation = validateBidData(formData);
    setValidationErrors(validation.errors);

    if (!validation.isValid) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors below before submitting.',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await createBid(formData);

      if (response.isSuccess) {
        toast({
          title: 'Bid Submitted Successfully',
          description: 'Your bid has been submitted and is now pending review.',
        });
        onBidCreated?.();
      } else {
        // Enhanced error handling based on response
        const errorMessage = response.error || 'Failed to submit bid. Please try again.';

        // Handle specific error codes if available
        if (response.status === 400) {
          toast({
            title: 'Invalid Request',
            description: 'Please check your bid details and try again.',
            variant: 'destructive'
          });
        } else if (response.status === 401) {
          toast({
            title: 'Authentication Required',
            description: 'Please log in to submit a bid.',
            variant: 'destructive'
          });
        } else if (response.status === 403) {
          toast({
            title: 'Access Denied',
            description: 'You do not have permission to bid on this job.',
            variant: 'destructive'
          });
        } else if (response.status === 500) {
          toast({
            title: 'Server Error',
            description: 'A server error occurred. Please try again later.',
            variant: 'destructive'
          });
        } else {
          toast({
            title: 'Submission Failed',
            description: errorMessage,
            variant: 'destructive'
          });
        }
      }
    } catch (error) {
      console.error('Bid submission error:', error);
      toast({
        title: 'Network Error',
        description: 'Unable to connect to the server. Please check your connection and try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Submit Your Bid</CardTitle>
        <div className="text-sm text-gray-600">
          <p><strong>Job:</strong> {job.title}</p>
          <p><strong>Service Type:</strong> {job.serviceType}</p>
          {job.location && <p><strong>Location:</strong> {job.location}</p>}
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* General validation errors */}
          {validationErrors.length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please fix the following errors:
                <ul className="mt-2 list-disc list-inside">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error.message}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="amount" className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Bid Amount ($)
            </Label>
            <Input
              id="amount"
              type="number"
              min="0"
              step="0.01"
              value={formData.amount || ''}
              onChange={(e) => handleInputChange('amount', parseFloat(e.target.value) || 0)}
              onBlur={() => handleBlur('amount')}
              placeholder="Enter your bid amount"
              className={getFieldError(validationErrors, 'amount') && touched.amount ? 'border-red-500' : ''}
              required
            />
            {getFieldError(validationErrors, 'amount') && touched.amount && (
              <p className="text-sm text-red-600">{getFieldError(validationErrors, 'amount')}</p>
            )}
            <p className="text-xs text-gray-500">
              Enter a competitive amount based on the job requirements
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Description
            </Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              onBlur={() => handleBlur('description')}
              placeholder="Describe your approach, timeline, materials needed, and any additional details that make your bid stand out..."
              rows={4}
              className={getFieldError(validationErrors, 'description') && touched.description ? 'border-red-500' : ''}
              required
            />
            {getFieldError(validationErrors, 'description') && touched.description && (
              <p className="text-sm text-red-600">{getFieldError(validationErrors, 'description')}</p>
            )}
            <p className="text-xs text-gray-500">
              {formData.description.length}/2000 characters (minimum 10 required)
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="estimated_completion_time" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Estimated Completion Time
            </Label>
            <Input
              id="estimated_completion_time"
              type="datetime-local"
              value={formData.estimated_completion_time}
              onChange={(e) => handleInputChange('estimated_completion_time', e.target.value)}
              onBlur={() => handleBlur('estimated_completion_time')}
              className={getFieldError(validationErrors, 'estimated_completion_time') && touched.estimated_completion_time ? 'border-red-500' : ''}
              required
            />
            {getFieldError(validationErrors, 'estimated_completion_time') && touched.estimated_completion_time && (
              <p className="text-sm text-red-600">{getFieldError(validationErrors, 'estimated_completion_time')}</p>
            )}
            <p className="text-xs text-gray-500">
              When do you expect to complete this job?
            </p>
          </div>

          <div className="flex gap-3 justify-end">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Bid'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};