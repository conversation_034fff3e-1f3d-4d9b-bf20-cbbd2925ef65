import React, { useState } from 'react';
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { CreateBidForm } from './CreateBidForm';
import { Job } from '@/types/jobs';
import { useNavigate } from 'react-router-dom';

interface BidModalProps {
  isOpen: boolean;
  onClose: () => void;
  job: Job;
}

export const BidModal: React.FC<BidModalProps> = ({ isOpen, onClose, job }) => {
  const navigate = useNavigate();

  const handleBidCreated = () => {
    onClose();
    // Navigate to provider bids page to see the submitted bid
    navigate('/provider/bids');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Submit Your Bid</DialogTitle>
        </DialogHeader>
        <div className="mt-4">
          <CreateBidForm
            job={job}
            onBidCreated={handleBidCreated}
            onCancel={onClose}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
