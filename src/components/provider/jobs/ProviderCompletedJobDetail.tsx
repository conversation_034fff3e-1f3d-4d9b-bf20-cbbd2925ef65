import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  ArrowLeft, 
  MapPin, 
  Calendar,
  DollarSign,
  Clock,
  MessageSquare,
  Phone,
  Mail,
  CheckCircle,
  AlertCircle,
  Loader2,
  Star,
  FileText
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { apiService } from "@/services/api";

interface JobDetail {
  id: string;
  title: string;
  description: string;
  customer: {
    name: string;
    avatar?: string;
    email?: string;
    phone?: string;
  };
  location: {
    address: string;
    city: string;
    state: string;
  };
  schedule: {
    date: string;
    time?: string;
  };
  budget: number;
  status: string;
  createdAt: string;
  completedAt?: string;
  service: {
    category: string;
    tasks: string[];
  };
  review?: {
    rating: number;
    comment: string;
    createdAt: string;
  };
}

export function ProviderCompletedJobDetail() {
  const { jobId } = useParams<{ jobId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { token } = useAuth();
  
  const [job, setJob] = useState<JobDetail | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (jobId) {
      fetchJobDetails();
    }
  }, [jobId]);

  const fetchJobDetails = async () => {
    if (!jobId || !token) return;

    setIsLoading(true);
    try {
      const response = await apiService<{ data: any }>(`/api/job-bookings/${jobId}`, {
        method: 'GET',
        requiresAuth: true,
        headers: {
          'Authorization': `Bearer ${token.replace('Bearer ', '')}`
        }
      });

      if (response.isSuccess && response.data) {
        // Transform API response to match our interface
        const jobData = response.data.data;
        setJob({
          id: jobData.id,
          title: jobData.service?.tasks?.join(", ") || jobData.service?.category || 'Service Request',
          description: jobData.description || 'No description provided',
          customer: {
            name: jobData.user?.name || 'Unknown Customer',
            avatar: jobData.user?.avatar,
            email: jobData.user?.email,
            phone: jobData.user?.phone
          },
          location: {
            address: jobData.location?.address || '',
            city: jobData.location?.city || '',
            state: jobData.location?.state || ''
          },
          schedule: {
            date: jobData.schedule?.date || jobData.createdAt,
            time: jobData.schedule?.time
          },
          budget: jobData.budget || 0,
          status: jobData.status || 'Completed',
          createdAt: jobData.createdAt,
          completedAt: jobData.completedAt,
          service: {
            category: jobData.service?.category || 'General Service',
            tasks: jobData.service?.tasks || []
          },
          review: jobData.review ? {
            rating: jobData.review.rating,
            comment: jobData.review.comment,
            createdAt: jobData.review.createdAt
          } : undefined
        });
      }
    } catch (error) {
      console.error('Error fetching job details:', error);
      toast({
        title: "Error",
        description: "Failed to load job details",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/provider/jobs');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!job) {
    return (
      <div className="space-y-6">
        <Button variant="ghost" onClick={handleBack} className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Jobs
        </Button>
        <Card>
          <CardContent className="py-8 text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Job not found</h3>
            <p className="text-muted-foreground">
              This job may have been removed or you don't have access to it.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Jobs
        </Button>
        <Badge variant="secondary" className="bg-green-100 text-green-800">
          <CheckCircle className="h-3 w-3 mr-1" />
          {job.status}
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Job Details */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">{job.title}</CardTitle>
              <p className="text-muted-foreground">{job.service.category}</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Description</h4>
                <p className="text-muted-foreground">{job.description}</p>
              </div>
              
              {job.service.tasks.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Tasks Completed</h4>
                  <ul className="list-disc list-inside text-muted-foreground space-y-1">
                    {job.service.tasks.map((task, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                        {task}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Location</p>
                    <p className="text-sm text-muted-foreground">
                      {job.location.address && `${job.location.address}, `}
                      {job.location.city}, {job.location.state}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Completed</p>
                    <p className="text-sm text-muted-foreground">
                      {job.completedAt ? formatDate(job.completedAt) : formatDate(job.schedule.date)}
                    </p>
                  </div>
                </div>

                {job.budget > 0 && (
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Payment</p>
                      <p className="text-sm text-muted-foreground">
                        ${job.budget.toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Started</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(job.createdAt)}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Customer Review */}
          {job.review && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Star className="h-5 w-5 text-yellow-400" />
                  Customer Review
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <div className="flex">{renderStars(job.review.rating)}</div>
                  <span className="text-sm text-muted-foreground">
                    {job.review.rating}/5 stars
                  </span>
                </div>
                <p className="text-muted-foreground">{job.review.comment}</p>
                <p className="text-xs text-muted-foreground">
                  Reviewed on {formatDate(job.review.createdAt)}
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Customer Info & Actions */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Customer</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <Avatar>
                  <AvatarImage src={job.customer.avatar} />
                  <AvatarFallback>
                    {job.customer.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{job.customer.name}</p>
                </div>
              </div>

              <div className="space-y-2">
                {job.customer.email && (
                  <div className="flex items-center gap-2 text-sm">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span>{job.customer.email}</span>
                  </div>
                )}
                {job.customer.phone && (
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{job.customer.phone}</span>
                  </div>
                )}
              </div>

              <Separator />

              <div className="space-y-2">
                <Button className="w-full" variant="outline">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Message Customer
                </Button>
                {job.customer.phone && (
                  <Button className="w-full" variant="outline">
                    <Phone className="h-4 w-4 mr-2" />
                    Call Customer
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Job Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Job Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-2" />
                <p className="font-medium text-green-700">Job Completed Successfully</p>
              </div>
              
              <Separator />
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Status:</span>
                  <span className="font-medium">{job.status}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Payment:</span>
                  <span className="font-medium">${job.budget.toLocaleString()}</span>
                </div>
                {job.review && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Rating:</span>
                    <span className="font-medium">{job.review.rating}/5 stars</span>
                  </div>
                )}
              </div>

              <Button className="w-full" variant="outline">
                <FileText className="h-4 w-4 mr-2" />
                Download Invoice
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
