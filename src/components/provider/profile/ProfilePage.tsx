
import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {Check, Upload, MapPin, Info, X, FileText, Image as ImageIcon, Loader2} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { servicesData } from '@/data/menu/service';
import { Slider } from '@/components/ui/slider';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useAddressAutocomplete } from '@/hooks/use-address-autocomplete';
import { useAuth } from "@/features/auth/hooks/useAuth.ts";
import { assetsService } from '@/services/assetsService';
import {apiService} from "@/services/api.ts";

export const ProfilePage = () => {
  const { toast } = useToast();
  const {user, token} = useAuth();
  const [isSaving, setIsSaving] = useState(false);
  const businessCertificationsRef = useRef<HTMLDivElement>(null);
  const [businessName, setBusinessName] = useState(user?.name);
  const [description, setDescription] = useState('Professional plumbing services with 15+ years of experience. Specializing in residential and commercial repairs, installations, and maintenance.');
  const [selectedServices, setSelectedServices] = useState(['plumbing']);
  const [additionalServiceAreas, setAdditionalServiceAreas] = useState('');
  const [profileCompletionPercent, setProfileCompletionPercent] = useState(70);
  // New state variables for location and service radius
  const [primaryZipCode, setPrimaryZipCode] = useState('98101');
  const [serviceRadius, setServiceRadius] = useState(25);

  // File upload state
  const [uploadedFiles, setUploadedFiles] = useState<Array<{
    name: string;
    size: number;
    url: string;
    type: string;
    id: string;
  }>>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { 
    query, 
    setQuery, 
    suggestions, 
    isLoading,
    handleSelectAddress 
  } = useAddressAutocomplete({ debounceMs: 500 });

  // Handle scrolling to business certifications section when hash is present
  useEffect(() => {
    if (window.location.hash === '#business-certifications' && businessCertificationsRef.current) {
      setTimeout(() => {
        businessCertificationsRef.current?.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start' 
        });
        // Add a subtle highlight effect
        businessCertificationsRef.current?.classList.add('ring-2', 'ring-blue-500', 'ring-opacity-50');
        setTimeout(() => {
          businessCertificationsRef.current?.classList.remove('ring-2', 'ring-blue-500', 'ring-opacity-50');
        }, 3000);
      }, 100);
    }
  }, []);

  const handleServiceToggle = (serviceId: string) => {
    if (selectedServices.includes(serviceId)) {
      setSelectedServices(selectedServices.filter((id) => id !== serviceId));
    } else {
      setSelectedServices([...selectedServices, serviceId]);
    }
  };

  // Handler for primary zipcode change
  const handlePrimaryZipCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPrimaryZipCode(e.target.value);
    setQuery(e.target.value);
  };
  
  // Handler for service radius slider
  const handleServiceRadiusChange = (values: number[]) => {
    setServiceRadius(values[0]);
  };

  // File upload handlers
  const handleFileSelect = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    // Validate files
    const validFiles: File[] = [];
    const maxSize = 10 * 1024 * 1024;
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    Array.from(files).forEach(file => {
      if (file.size > maxSize) {
        toast({
          title: "File too large",
          description: `${file.name} exceeds the 10MB size limit.`,
          variant: "destructive"
        });
        return;
      }

      if (!allowedTypes.includes(file.type)) {
        toast({
          title: "Invalid file type",
          description: `${file.name} is not a supported file type.`,
          variant: "destructive"
        });
        return;
      }

      validFiles.push(file);
    });

    if (validFiles.length === 0) return;

    // Upload files
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 10;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 300);

      const response = await assetsService.uploadFiles(validFiles);
      clearInterval(progressInterval);
      setUploadProgress(100);

      if (response.isSuccess && response?.data) {
        const newFiles = validFiles.map((file, index) => ({
          name: file.name,
          size: file.size,
          url: response?.data?.urls[index] || '',
          type: file.type,
          id: response?.data?.id || ''
        }));

        setUploadedFiles(prev => [...prev, ...newFiles]);
        toast({
          title: "Files uploaded",
          description: `Successfully uploaded ${newFiles.length} file(s).`,
        });
      } else {
        toast({
          title: "Upload failed",
          description: response.error || "Failed to upload files. Please try again.",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Upload error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
    toast({
      title: "File removed",
      description: "The file has been removed from your profile.",
    });
  };


  const handleRequest = async () => {
    setIsSaving(true);
    const payload = uploadedFiles.map((file) => {
      return file.id;
    });

    try {
      const { data, error, isSuccess } = await apiService(`/api/user/certificates`, {
        method: 'PATCH',
        headers: {
          'Authorization': token || '',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: { certificates: payload },
      });

      await apiService(`/api/user/certificates/request-review`, {
        method: 'POST',
        headers: {
          'Authorization': token || '',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
      });

      if (isSuccess) {
        console.log("data", data);
        setUploadedFiles([]);
        toast({
          title: "Request sent",
          description: "Your request has been sent. We will get back to you soon.",
          className: "bg-green-400 text-white",
        });
      } else {
        console.log("error", error);
        toast({
          title: "Error",
          description: "Failed to send request. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error(error);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card id="business-info">
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <CardTitle>Business Profile</CardTitle>
              <CardDescription>
                Complete your business profile to attract more customers
              </CardDescription>
            </div>
            <div className="flex flex-col items-center md:items-end">
              <div className="text-sm font-medium mb-1">Profile Completion</div>
              <div className="flex items-center gap-2 w-full max-w-xs">
                <Progress value={profileCompletionPercent} className="h-2 flex-1" />
                <span className="text-xs font-medium">{profileCompletionPercent}%</span>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex flex-col md:flex-row gap-6 items-start">
            <div className="flex flex-col items-center gap-2">
              <Avatar className="h-24 w-24">
                <AvatarImage src="/placeholder.svg" alt="Business Logo" />
                <AvatarFallback>JP</AvatarFallback>
              </Avatar>
              <Button variant="outline" size="sm" className="mt-2">
                <Upload className="h-4 w-4 mr-2" /> Upload Logo
              </Button>
            </div>
            
            <div className="flex-1 space-y-4 w-full">
              <div className="space-y-2">
                <Label htmlFor="businessName">Business Name</Label>
                <Input
                  id="businessName"
                  value={businessName}
                  onChange={(e) => setBusinessName(e.target.value)}
                  placeholder="Your business name"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">Business Description</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Describe your business and services..."
                  rows={4}
                />
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Services Offered</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
                {servicesData.map((service) => (
                  <div
                    key={service.id}
                    onClick={() => handleServiceToggle(service.id)}
                    className={`flex items-center p-2 border rounded-md cursor-pointer transition-colors ${
                      selectedServices.includes(service.id)
                        ? 'bg-primary/10 border-primary'
                        : 'hover:bg-secondary/20'
                    }`}
                  >
                    <div className={`p-2 rounded-md mr-2 ${service.bgColor}`}>
                      <service.icon className={`h-4 w-4 ${service.textColor}`} />
                    </div>
                    <span className="text-sm font-medium">{service.name}</span>
                    {selectedServices.includes(service.id) && (
                      <Check className="h-4 w-4 text-primary ml-auto" />
                    )}
                  </div>
                ))}
              </div>
            </div>
            
            {/* New Service Areas Section */}
            <div className="border rounded-lg p-4 space-y-6 bg-gray-50 dark:bg-gray-900/50">
              <div>
                <h3 className="text-lg font-medium mb-2">Service Location & Coverage</h3>
                <p className="text-sm text-muted-foreground">
                  Define where your business is based and how far you're willing to travel for jobs
                </p>
              </div>
              
              {/* Primary Business Location */}
              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="primaryZipCode" className="flex items-center">
                    Business ZIP Code
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 ml-1.5 text-muted-foreground cursor-help" />
                        </TooltipTrigger>
                        <TooltipContent side="right">
                          <p className="max-w-xs text-xs">
                            Enter the ZIP code where your business is primarily located.
                            This helps customers find services in their area.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </Label>
                </div>
                <div className="relative">
                  <div className="flex">
                    <MapPin className="h-4 w-4 absolute left-3 top-3 text-muted-foreground" />
                    <Input
                      id="primaryZipCode"
                      value={primaryZipCode}
                      onChange={handlePrimaryZipCodeChange}
                      placeholder="Enter your business ZIP code"
                      className="pl-9"
                    />
                  </div>
                  
                  {/* ZIP code validation suggestions would appear here */}
                  {suggestions.length > 0 && query.length > 0 && (
                    <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 shadow-lg rounded-md border">
                      {suggestions.map((suggestion, index) => (
                        <div 
                          key={index}
                          className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                          onClick={() => {
                            handleSelectAddress(suggestion);
                            setPrimaryZipCode(suggestion.zipCode);
                          }}
                        >
                          {suggestion.city}, {suggestion.state} {suggestion.zipCode}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              
              {/* Service Radius Slider */}
              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="serviceRadius" className="flex items-center">
                    How far are you willing to service outside your location?
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 ml-1.5 text-muted-foreground cursor-help" />
                        </TooltipTrigger>
                        <TooltipContent side="right">
                          <p className="max-w-xs text-xs">
                            This determines how far from your primary ZIP code you'll be notified about potential jobs.
                            A larger radius means more job opportunities but might involve more travel.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </Label>
                </div>
                
                <div className="pt-4 pl-1 pr-1">
                  <Slider 
                    value={[serviceRadius]} 
                    min={5}
                    max={100}
                    step={5}
                    onValueChange={handleServiceRadiusChange}
                  />
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm text-muted-foreground">5 miles</span>
                    <span className="text-sm font-medium">{serviceRadius} miles</span>
                    <span className="text-sm text-muted-foreground">100 miles</span>
                  </div>
                </div>
              </div>
              
              {/* Additional Service Areas */}
              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="additionalServiceAreas" className="flex items-center">
                    Additional Service Areas (Optional)
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 ml-1.5 text-muted-foreground cursor-help" />
                        </TooltipTrigger>
                        <TooltipContent side="right">
                          <p className="max-w-xs text-xs">
                            If you service specific areas beyond your radius, enter the ZIP codes here.
                            Separate multiple ZIP codes with commas.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </Label>
                </div>
                <Textarea
                  id="additionalServiceAreas"
                  value={additionalServiceAreas}
                  onChange={(e) => setAdditionalServiceAreas(e.target.value)}
                  placeholder="Enter additional ZIP codes separated by commas (e.g., 98102, 98103, 98105)"
                  rows={2}
                />
                <p className="text-xs text-muted-foreground">
                  Add specific ZIP codes beyond your service radius where you're also willing to work
                </p>
              </div>
            </div>
            
            <div className="space-y-2" ref={businessCertificationsRef}>
              <Label>Business Certifications & Licenses</Label>
              <div className="border border-dashed rounded-md p-6 text-center">
                <div className="flex flex-col items-center">
                  <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-sm font-medium mb-1">
                    Drop your files here or click to upload
                  </p>
                  <p className="text-xs text-muted-foreground mb-4">
                    Upload business licenses, certifications, or insurance documents
                  </p>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    className="hidden"
                    multiple
                    accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx"
                  />
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={handleFileSelect}
                    disabled={isUploading}
                  >
                    {isUploading ? "Uploading..." : "Select Files"}
                  </Button>
                </div>
              </div>

              {/* Upload Progress */}
              {isUploading && (
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Uploading files...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} className="h-2" />
                </div>
              )}

              {/* Uploaded Files List */}
              {uploadedFiles.length > 0 && (
                <div className="mt-4 space-y-3">
                  <h4 className="text-sm font-medium">Uploaded Files</h4>
                  <div className="space-y-2">
                    {uploadedFiles.map(file => (
                      <div key={file.id} className="flex items-center justify-between p-3 border border-[#2463EB] bg-[#EAEFFD] rounded-md bg-background">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-primary/10 rounded-md">
                            {file.type.startsWith('image/') ? (
                              <ImageIcon className="h-5 w-5 text-primary" />
                            ) : (
                              <FileText className="h-5 w-5 text-primary" />
                            )}
                          </div>
                          <div>
                            <p className="text-sm font-medium truncate max-w-[200px]">{file.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {(file.size / 1024).toFixed(1)} KB
                            </p>
                          </div>
                        </div>

                        {/* Preview for images */}
                        {file.type.startsWith('image/') && (
                          <div className="h-10 w-10 rounded-md overflow-hidden mr-2">
                            <img 
                              src={file.url} 
                              alt={file.name} 
                              className="h-full w-full object-cover"
                            />
                          </div>
                        )}

                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => handleRemoveFile(file.id)}
                          className="text-destructive hover:text-destructive/90"
                        >
                          <X className="h-8 w-8" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row justify-end gap-4">
          <Button
            variant="outline"
            type="submit"
            onClick={handleRequest}
            disabled={isSaving}
            className="w-full sm:w-auto"
          >
            {isSaving ? (
              <>
                <Loader2 className="size-4 animate-spin" />
                <span>Sending Request...</span>
              </>
            ) : (
              "Request Verification Badge"
            )}
          </Button>

        </CardFooter>
      </Card>
      
      <Card id="contact-verification">
        <CardHeader>
          <CardTitle>Contact Verification</CardTitle>
          <CardDescription>
            Verify your contact information to build trust with customers
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex justify-between items-center border p-4 rounded-md">
              <div className="flex items-center">
                <div className="mr-4 p-2 bg-green-50 rounded-full">
                  <Check className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <h3 className="font-medium">Email Address</h3>
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                Verified
              </Badge>
            </div>
            
            <div className="flex justify-between items-center border p-4 rounded-md">
              <div className="flex items-center">
                <div className="mr-4 p-2 bg-gray-100 rounded-full">
                  <Check className="h-5 w-5 text-gray-400" />
                </div>
                <div>
                  <h3 className="font-medium">Phone Number</h3>
                  <p className="text-sm text-muted-foreground">(*************</p>
                </div>
              </div>
              <Button variant="outline" size="sm">
                Verify Now
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
