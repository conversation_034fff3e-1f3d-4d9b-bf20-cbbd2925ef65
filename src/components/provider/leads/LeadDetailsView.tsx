
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Calendar, MapPin, Clock, DollarSign, Calendar as CalendarIcon, ArrowLeft } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import { getPreviousPage } from '@/utils/navigationUtils';
import { apiService } from '@/services/api';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { createBid } from '@/services/bidService';

// Define lead data type based on API response
interface Lead {
  id: string;
  title: string;
  description: string;
  location: {
    city: string;
    state: string;
    fullAddress?: string;
    address?: string;
    zipCode?: string;
  };
  postedAt: string;
  budget?: number;
  category: string;
  urgent?: boolean;
  requestedDate?: string;
  customerName?: string;
  photos?: string[];
  service?: {
    category: string;
    tasks?: string[];
  };
  schedule?: {
    date: string;
    timePreference?: string;
  };
  assets?: Array<{
    url: string;
    uuid: string;
  }>;
  contact?: {
    fullName: string;
  };
  user?: {
    name: string;
  };
  createdAt: string;
  status: string;
}

// Define bid form schema
const bidFormSchema = z.object({
  amount: z.string().min(1, "Amount is required").refine(val => !isNaN(Number(val)), {
    message: "Amount must be a valid number",
  }),
  message: z.string().min(10, "Message must be at least 10 characters"),
  availableDate: z.string().min(1, "Available date is required"),
  estimatedCompletionTime: z.string().min(1, "Estimated completion time is required"),
});

type BidFormValues = z.infer<typeof bidFormSchema>;

export const LeadDetailsView = ({ jobId }: { jobId: string }) => {
  const [lead, setLead] = useState<Lead | null>(null);
  const [loading, setLoading] = useState(true);
  const [bidDialogOpen, setBidDialogOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { token } = useAuth();

  const form = useForm<BidFormValues>({
    resolver: zodResolver(bidFormSchema),
    defaultValues: {
      amount: "",
      message: "",
      availableDate: "",
      estimatedCompletionTime: "",
    },
  });

  useEffect(() => {
    const fetchLeadDetails = async () => {
      if (!jobId || !token) {
        console.log('Missing jobId or token:', { jobId, token });
        return;
      }

      setLoading(true);

      try {
        // token from useAuthHeader already includes 'Bearer ' prefix
        const authToken = token || '';
        console.log('Auth token for lead details:', authToken); // Debug log

        const response = await apiService(`/api/job-bookings/${jobId}`, {
          method: 'GET',
          requiresAuth: true,
          headers: {
            'Authorization': authToken,
            'Content-Type': 'application/json'
          }
        });

        if (response.isSuccess && response.data) {
          const jobData = response.data.data || response.data;

          // Format relative time
          const formatRelativeTime = (dateString: string) => {
            const date = new Date(dateString);
            const now = new Date();
            const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

            if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
            const diffInMinutes = Math.floor(diffInSeconds / 60);
            if (diffInMinutes < 60) return `${diffInMinutes} ${diffInMinutes === 1 ? 'minute' : 'minutes'} ago`;
            const diffInHours = Math.floor(diffInMinutes / 60);
            if (diffInHours < 24) return `${diffInHours} ${diffInHours === 1 ? 'hour' : 'hours'} ago`;
            const diffInDays = Math.floor(diffInHours / 24);
            return `${diffInDays} ${diffInDays === 1 ? 'day' : 'days'} ago`;
          };

          // Map API response to Lead interface
          const mappedLead: Lead = {
            id: jobData.id,
            title: jobData.service?.category
              ? `${jobData.service.category}${jobData.service.tasks ? ` - ${jobData.service.tasks.join(', ')}` : ''}`
              : 'Service Request',
            description: jobData.description || 'No description provided',
            location: {
              city: jobData.location?.city || 'Unknown',
              state: jobData.location?.state || 'Unknown',
              fullAddress: jobData.location?.address || `${jobData.location?.city || 'Unknown'}, ${jobData.location?.state || 'Unknown'}`,
              address: jobData.location?.address,
              zipCode: jobData.location?.zipCode
            },
            postedAt: formatRelativeTime(jobData.createdAt),
            budget: typeof jobData.budget === 'number' ? jobData.budget : (jobData.budget ? parseInt(jobData.budget) : undefined),
            category: jobData.service?.category || 'General',
            urgent: jobData.status === 'urgent',
            requestedDate: jobData.schedule?.date,
            customerName: jobData.contact?.fullName || jobData.user?.name || 'Customer',
            photos: jobData.assets?.map((asset: any) => `https://dash.jobon.app/storage/${asset.url}`) || [],
            service: jobData.service,
            schedule: jobData.schedule,
            assets: jobData.assets,
            contact: jobData.contact,
            user: jobData.user,
            createdAt: jobData.createdAt,
            status: jobData.status
          };

          setLead(mappedLead);

          // Set default bid amount if budget exists
          if (mappedLead.budget) {
            form.setValue("amount", mappedLead.budget.toString());
          }
        } else {
          throw new Error(response.error || 'Failed to fetch lead details');
        }
      } catch (error) {
        console.error("Error fetching lead details:", error);
        toast({
          title: "Error",
          description: "Failed to load lead details. Please try again.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchLeadDetails();
  }, [jobId, token, form, toast]);

  const onSubmitBid = async (values: BidFormValues) => {
    if (!lead || !token) return;

    setSubmitting(true);

    try {
      // token from useAuthHeader already includes 'Bearer ' prefix
      const authToken = token || '';
      console.log('Auth token for bid submission:', authToken); // Debug log

      // Prepare bid data using new API format
      const bidData = {
        amount: parseFloat(values.amount),
        description: values.message,
        estimated_completion_time: values.estimatedCompletionTime
      };

      console.log('Bid data:', bidData); // Debug log

      const response = await apiService(`/api/job-bookings/${lead.id}/bids`, {
        method: 'POST',
        requiresAuth: true,
        headers: {
          'Authorization': authToken,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(bidData)
      });

      if (response.isSuccess) {
        // Show success toast
        toast({
          title: "Bid submitted successfully!",
          description: "The customer will be notified of your offer.",
        });

        // Close dialog and redirect
        setBidDialogOpen(false);
        navigate('/provider/jobs?tab=leads');
      } else {
        throw new Error(response.error || 'Failed to submit bid');
      }
    } catch (error) {
      console.error("Error submitting bid:", error);
      toast({
        title: "Failed to submit bid",
        description: error instanceof Error ? error.message : "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleGoBack = () => {
    navigate('/provider/jobs?tab=leads');
  };

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center mb-4">
          <Button 
            variant="ghost" 
            size="sm" 
            className="gap-1" 
            onClick={handleGoBack}
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Leads
          </Button>
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <div className="flex flex-wrap gap-4 mt-4">
              <Skeleton className="h-24 w-24 rounded-md" />
              <Skeleton className="h-24 w-24 rounded-md" />
            </div>
          </CardContent>
          <CardFooter>
            <Skeleton className="h-10 w-32" />
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (!lead) {
    return (
      <div className="space-y-6">
        <div className="flex items-center mb-4">
          <Button 
            variant="ghost" 
            size="sm" 
            className="gap-1" 
            onClick={handleGoBack}
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Leads
          </Button>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Lead not found</CardTitle>
            <CardDescription>This lead may have been removed or is no longer available.</CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={handleGoBack}>Return to Leads</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Back button */}
      <div className={`flex items-center ${isMobile ? 'mb-2' : 'mb-4'}`}>
        <Button 
          variant="ghost" 
          size={isMobile ? "sm" : "default"}
          className="gap-1 hover:bg-gray-100 dark:hover:bg-gray-800" 
          onClick={handleGoBack}
        >
          <ArrowLeft className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />
          <span className="font-medium">Back to Leads</span>
        </Button>
      </div>

      <Card className="border-l-4 border-l-primary shadow-sm">
        <CardHeader className="flex flex-row items-start justify-between">
          <div>
            <div className="flex items-center gap-2">
              <CardTitle className="text-xl">{lead.title}</CardTitle>
              {lead.urgent && (
                <Badge variant="destructive">Urgent</Badge>
              )}
            </div>
            <CardDescription className="mt-1.5">Posted {lead.postedAt}</CardDescription>
          </div>
          <Badge variant="outline">{lead.category}</Badge>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Details section */}
          <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3">
            <div className="flex flex-col space-y-1">
              <span className="text-sm font-medium text-gray-500">Location</span>
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-1 text-gray-500" />
                <span>{lead.location.fullAddress || `${lead.location.city}, ${lead.location.state}`}</span>
              </div>
            </div>
            {lead.requestedDate && (
              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-gray-500">Requested Date</span>
                <div className="flex items-center">
                  <CalendarIcon className="h-4 w-4 mr-1 text-gray-500" />
                  <span>{new Date(lead.requestedDate).toLocaleDateString()}</span>
                </div>
              </div>
            )}
            {lead.budget && (
              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-gray-500">Budget</span>
                <div className="flex items-center">
                  <DollarSign className="h-4 w-4 mr-1 text-gray-500" />
                  <span>${lead.budget}</span>
                </div>
              </div>
            )}
          </div>

          {/* Description */}
          <div>
            <h3 className="text-lg font-medium mb-2">Description</h3>
            <p className="text-gray-700 whitespace-pre-line">{lead.description}</p>
          </div>

          {/* Photos */}
          {lead.photos && lead.photos.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-2">Photos</h3>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {lead.photos.map((photo, index) => (
                  <div 
                    key={index} 
                    className="relative aspect-square rounded-md overflow-hidden border border-gray-200"
                  >
                    <img
                      src={photo}
                      alt={`Job photo ${index + 1}`}
                      className="object-cover w-full h-full"
                    />
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className={isMobile ? "flex-col space-y-2" : ""}>
          <Button 
            onClick={() => setBidDialogOpen(true)} 
            className={isMobile ? "w-full" : ""}
          >
            Submit Bid
          </Button>
          {isMobile && (
            <Button 
              variant="outline" 
              onClick={handleGoBack} 
              className="w-full"
            >
              Back to Leads
            </Button>
          )}
        </CardFooter>
      </Card>

      {/* Bid Dialog */}
      <Dialog open={bidDialogOpen} onOpenChange={setBidDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Submit a Bid</DialogTitle>
            <DialogDescription>
              Enter your bid details for "{lead.title}".
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmitBid)} className="space-y-4">
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bid Amount ($)</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="Enter amount" {...field} />
                    </FormControl>
                    <FormDescription>
                      Customer's budget: ${lead.budget}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="availableDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>When can you start?</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="estimatedCompletionTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Estimated Completion Time</FormLabel>
                    <FormControl>
                      <Input type="datetime-local" {...field} />
                    </FormControl>
                    <FormDescription>
                      When do you expect to complete this job?
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Message to Customer</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Introduce yourself and explain why you're a good fit for this job..."
                        className="min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Highlight your experience and approach to the job.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  type="button" 
                  onClick={() => setBidDialogOpen(false)}
                  disabled={submitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={submitting}>
                  {submitting ? "Submitting..." : "Submit Bid"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
};
