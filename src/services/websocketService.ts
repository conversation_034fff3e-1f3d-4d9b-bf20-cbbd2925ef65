import { WebSocketMessage, JoinChatMessage, MessageSentMessage } from '@/types/chat';

/**
 * WebSocket Service for real-time chat functionality
 * Handles connection, message sending, and event listening
 */
export class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private maxReconnectDelay = 30000; // Max 30 seconds
  private isIntentionallyClosed = false;
  private currentChatId: string | null = null;
  
  // Event listeners
  private onMessageCallbacks: ((message: WebSocketMessage) => void)[] = [];
  private onConnectCallbacks: (() => void)[] = [];
  private onDisconnectCallbacks: (() => void)[] = [];
  private onErrorCallbacks: ((error: Event) => void)[] = [];

  constructor(url?: string) {
    // Use environment variable or default to localhost
    this.url =  'wss://dash.jobon.app/ws';
  }

  /**
   * Connect to WebSocket server
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        console.log('Attempting to connect to WebSocket:', this.url);
        this.isIntentionallyClosed = false;

        // Check if URL is valid
        try {
          new URL(this.url);
        } catch (urlError) {
          const error = new Error(`Invalid WebSocket URL: ${this.url}`);
          console.error('WebSocket URL validation failed:', error);
          reject(error);
          return;
        }

        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('WebSocket connected successfully to:', this.url);
          this.reconnectAttempts = 0;
          this.reconnectDelay = 1000;

          // Rejoin current chat if we were in one
          if (this.currentChatId) {
            console.log('Rejoining chat:', this.currentChatId);
            this.joinChat(this.currentChatId);
          }

          this.onConnectCallbacks.forEach(callback => callback());
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            console.log('WebSocket message received:', message);
            this.onMessageCallbacks.forEach(callback => callback(message));
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error, 'Raw data:', event.data);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', {
            code: event.code,
            reason: event.reason,
            wasClean: event.wasClean,
            url: this.url
          });
          this.ws = null;
          this.onDisconnectCallbacks.forEach(callback => callback());

          // Auto-reconnect if not intentionally closed
          if (!this.isIntentionallyClosed && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          } else if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.warn('Max reconnection attempts reached. Giving up.');
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error occurred:', {
            error,
            url: this.url,
            readyState: this.ws?.readyState,
            reconnectAttempts: this.reconnectAttempts
          });
          this.onErrorCallbacks.forEach(callback => callback(error));
          reject(new Error(`WebSocket connection failed to ${this.url}. Please check if the WebSocket server is running.`));
        };

        // Set a timeout for connection attempt
        const connectionTimeout = setTimeout(() => {
          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            console.error('WebSocket connection timeout');
            this.ws.close();
            reject(new Error(`WebSocket connection timeout to ${this.url}`));
          }
        }, 10000); // 10 second timeout

        // Clear timeout on successful connection
        this.ws.addEventListener('open', () => {
          clearTimeout(connectionTimeout);
        });

      } catch (error) {
        console.error('Failed to create WebSocket connection:', error);
        reject(error);
      }
    });
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    this.isIntentionallyClosed = true;
    this.currentChatId = null;
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnecting');
      this.ws = null;
    }
  }

  /**
   * Check if WebSocket is connected
   */
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * Send a message through WebSocket
   */
  private send(message: WebSocketMessage): void {
    if (this.isConnected() && this.ws) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected. Cannot send message:', message);
    }
  }

  /**
   * Join a chat room to receive real-time updates
   */
  joinChat(chatId: string): void {
    this.currentChatId = chatId;
    
    const joinMessage: JoinChatMessage = {
      action: 'join',
      chat_id: chatId
    };
    
    this.send(joinMessage);
  }

  /**
   * Leave current chat room
   */
  leaveChat(): void {
    this.currentChatId = null;
    // Note: Backend documentation doesn't mention a leave action,
    // so we just clear the current chat ID
  }

  /**
   * Schedule reconnection with exponential backoff
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++;

    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${this.reconnectDelay}ms to ${this.url}`);

    setTimeout(() => {
      if (!this.isIntentionallyClosed) {
        console.log(`Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts}...`);
        this.connect().catch((error) => {
          console.error(`Reconnection attempt ${this.reconnectAttempts} failed:`, error);
          // Increase delay for next attempt (exponential backoff)
          this.reconnectDelay = Math.min(this.reconnectDelay * 2, this.maxReconnectDelay);
        });
      }
    }, this.reconnectDelay);
  }

  /**
   * Manually trigger reconnection
   */
  reconnect(): void {
    this.disconnect();
    this.reconnectAttempts = 0;
    this.reconnectDelay = 1000;
    this.connect();
  }

  // Event listener management
  onMessage(callback: (message: WebSocketMessage) => void): () => void {
    this.onMessageCallbacks.push(callback);
    return () => {
      const index = this.onMessageCallbacks.indexOf(callback);
      if (index > -1) {
        this.onMessageCallbacks.splice(index, 1);
      }
    };
  }

  onConnect(callback: () => void): () => void {
    this.onConnectCallbacks.push(callback);
    return () => {
      const index = this.onConnectCallbacks.indexOf(callback);
      if (index > -1) {
        this.onConnectCallbacks.splice(index, 1);
      }
    };
  }

  onDisconnect(callback: () => void): () => void {
    this.onDisconnectCallbacks.push(callback);
    return () => {
      const index = this.onDisconnectCallbacks.indexOf(callback);
      if (index > -1) {
        this.onDisconnectCallbacks.splice(index, 1);
      }
    };
  }

  onError(callback: (error: Event) => void): () => void {
    this.onErrorCallbacks.push(callback);
    return () => {
      const index = this.onErrorCallbacks.indexOf(callback);
      if (index > -1) {
        this.onErrorCallbacks.splice(index, 1);
      }
    };
  }
}

// Create singleton instance
export const websocketService = new WebSocketService();
