import { ApiResponse, apiService } from './api';

// Interface for email tracking statistics response
export interface EmailTrackingStatistics {
  total: number;
  delivered: number;
  opened: number;
  clicked: number;
  bounced: number;
  spam: number;
  unsubscribed: number;
  current_page: number;
  per_page: number;
  from: number;
  to: number;
  last_page: number;
  data: EmailTrackingData[];
}

// Wrapper interface for the API response structure
export interface EmailTrackingResponse {
  data: EmailTrackingStatistics;
}

// Interface for individual email tracking data
export interface EmailTrackingData {
  notification_id: string;
  sent_at: string;
  type: string;
  notifiable_type: string;
  notifiable_id: number;
  email: string;
  recipient_email: string;
  role_name: string;
  opened_at: string | null;
  open_count: number;
  click_count: number;
}

// Interface for daily metrics response
export interface DailyMetricsResponse {
  success: boolean;
  data: DailyMetric[];
}

// Interface for daily metric data
export interface DailyMetric {
  date: string; // Format: YYYY-MM-DD
  sent: number;
  opened: number;
  clicked: number;
}

// Interface for email distribution response
export interface EmailDistributionResponse {
  success: boolean;
  data: {
    total: number;
    distribution: EmailDistribution[];
  };
}

// Interface for email distribution data
export interface EmailDistribution {
  status: string; // "Unopened", "Opened", or "Clicked"
  count: number;
  percentage: number; // Decimal value (e.g., 75.5 for 75.5%)
}

// Email tracking service
export const emailTrackingService = {
  // Get email tracking statistics
  getEmailTrackingStatistics: (
    token: string,
    params: {
      type?: string;
      start_date?: string;
      end_date?: string;
      notification_type?: string;
      email?: string;
      per_page?: number;
      page?: number;
    }
  ): Promise<ApiResponse<EmailTrackingResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    // Build query parameters
    const queryParams = new URLSearchParams();
    if (params.type) queryParams.append('type', params.type);
    if (params.start_date) queryParams.append('start_date', params.start_date);
    if (params.end_date) queryParams.append('end_date', params.end_date);
    if (params.notification_type) queryParams.append('notification_type', params.notification_type);
    if (params.email) queryParams.append('email', params.email);
    if (params.per_page) queryParams.append('per_page', params.per_page.toString());
    if (params.page) queryParams.append('page', params.page.toString());

    const endpoint = `/api/email-tracking/statistics?${queryParams.toString()}`;

    return apiService<EmailTrackingResponse>(endpoint, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },

  // Get daily metrics for email tracking
  getDailyMetrics: (
    token: string,
    params: {
      start_date?: string;
      end_date?: string;
    }
  ): Promise<ApiResponse<DailyMetricsResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    // Build query parameters
    const queryParams = new URLSearchParams();
    if (params.start_date) queryParams.append('start_date', params.start_date);
    if (params.end_date) queryParams.append('end_date', params.end_date);

    const endpoint = `/api/email-tracking/analytics/daily-metrics?${queryParams.toString()}`;

    return apiService<DailyMetricsResponse>(endpoint, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },

  // Get email distribution data
  getEmailDistribution: (
    token: string,
    params: {
      start_date?: string;
      end_date?: string;
    }
  ): Promise<ApiResponse<EmailDistributionResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    // Build query parameters
    const queryParams = new URLSearchParams();
    if (params.start_date) queryParams.append('start_date', params.start_date);
    if (params.end_date) queryParams.append('end_date', params.end_date);

    const endpoint = `/api/email-tracking/analytics/distribution?${queryParams.toString()}`;

    return apiService<EmailDistributionResponse>(endpoint, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },
};