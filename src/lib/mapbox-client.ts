
// @ts-ignore - This is used because of a potential issue with @types/mapbox__mapbox-sdk not correctly declaring the module for services/geocoding.
// This allows the application to compile while the type definition issue can be investigated separately.
import MapboxClient from '@mapbox/mapbox-sdk/services/geocoding';

// Load Mapbox token from environment variable
const mapboxToken = import.meta.env.VITE_MAPBOX_TOKEN;

// Function to get token from localStorage if it exists
const getLocalMapboxToken = (): string => {
  if (typeof window === 'undefined') return '';
  
  const localToken = localStorage.getItem('mapbox_token');
  return localToken || '';
};

// Function to save token to localStorage
export const saveMapboxToken = (token: string): void => {
  if (typeof window === 'undefined') return;
  localStorage.setItem('mapbox_token', token);
  // Reload the page to reinitialize the client with the new token
  window.location.reload();
};

// Use environment token, fallback to localStorage token
const effectiveToken = mapboxToken || getLocalMapboxToken();

// Create client only if token is available
let mapboxClient: any = null;

if (effectiveToken) {
  try {
    mapboxClient = MapboxClient({ accessToken: effectiveToken });
    console.log('Mapbox client initialized successfully with token');
  } catch (err) {
    console.error('Failed to initialize Mapbox client:', err);
  }
}

export default mapboxClient;
