
import React, { useState, useEffect } from 'react';
import { ProviderDashboardLayout } from '@/components/provider/ProviderDashboardLayout';
import { SubscriptionPlanCard } from '@/components/provider/subscription/SubscriptionPlanCard';
import { BusinessCertificationDialog } from '@/components/provider/BusinessCertificationDialog';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/features/auth/hooks/useAuth';

import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { planService } from '@/services/planService';
import { ProviderPlan } from '@/components/admin/provider-tier/schemas';

const ProviderPlans = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string>('');
  const [plans, setPlans] = useState<ProviderPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Get current subscription from user data or default to 'starter'
  const getCurrentSubscription = () => {
    if (!user?.current_subscription) {
      return 'starter';
    }
    return user.current_subscription;
  };
  
  const [currentSubscription, setCurrentSubscription] = useState(getCurrentSubscription());
  
  // Fetch plans from API
  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await planService.getPlans(1, 50);
        
        if (response.success && response.data?.data) {
          // Filter only active plans
          const activePlans = response.data.data.filter(plan => 
            plan.features?.some(feature => 
              feature.text.toLowerCase().includes('active') ||
              feature.text.toLowerCase().includes('available')
            ) !== false // Include plans that don't have explicit status
          );
          setPlans(activePlans);
        } else {
          setError('Failed to load subscription plans');
        }
      } catch (err) {
        console.error('Error fetching plans:', err);
        setError('Failed to load subscription plans');
      } finally {
        setLoading(false);
      }
    };

    fetchPlans();
  }, []);
  
  // Transform API plan data to SubscriptionPlanCard props
  const transformPlanToCardProps = (plan: ProviderPlan) => {
    const basePrice = plan.price || 0;
    const currentSub = getCurrentSubscription();
    
    // Check if this plan is the current subscription
    // Compare with both plan.id and plan.name to handle different API response formats
    const isCurrentPlan = currentSub === plan.id || 
                         currentSub === plan.name || 
                         currentSub === plan.name.toLowerCase() ||
                         (currentSub === 'starter' && plan.name.toLowerCase() === 'starter');
    
    return {
      title: plan.name,
      price: `$${basePrice}`,
      description: plan.description || '',
      commission: plan.commission || '0%',
      isCurrentPlan: isCurrentPlan,
      isPopular: plan.name.toLowerCase() === 'pro',
      features: plan.features || [],
      ctaText: isCurrentPlan ? 'Current Plan' : `Upgrade to ${plan.name}`,
      onSelect: () => handleCheckout(plan.id || plan.name.toLowerCase()),
      disabled: isCurrentPlan,
    };
  };
  
  // Retry function for error state
  const retryFetch = () => {
    const fetchPlans = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await planService.getPlans(1, 50);
        
        if (response.success && response.data?.data) {
          const activePlans = response.data.data.filter(plan => 
            plan.features?.some(feature => 
              feature.text.toLowerCase().includes('active') ||
              feature.text.toLowerCase().includes('available')
            ) !== false
          );
          setPlans(activePlans);
        } else {
          setError('Failed to load subscription plans');
        }
      } catch (err) {
        console.error('Error fetching plans:', err);
        setError('Failed to load subscription plans');
      } finally {
        setLoading(false);
      }
    };

    fetchPlans();
  };
  
  // Show dialog to confirm business certification upload
  const handleCheckout = (planId: string) => {
    setSelectedPlan(planId);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setSelectedPlan('');
  };
  
  return (
    <ProviderDashboardLayout pageTitle="Plans & Pricing">
      <div className="space-y-6">
        {/* Monthly Plans Only */}
        <div className="space-y-6">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900">Monthly Plans</h2>
            <p className="text-gray-600 mt-2">Choose the perfect plan for your business needs</p>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2 text-lg">Loading plans...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={retryFetch} variant="outline">
                Try Again
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {plans.map((plan) => {
                const cardProps = transformPlanToCardProps(plan);
                return (
                  <SubscriptionPlanCard
                    key={plan.id || plan.name}
                    {...cardProps}
                  />
                );
              })}
            </div>
          )}
        </div>
      </div>
      
      {/* Business Certification Dialog */}
      <BusinessCertificationDialog
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
        planName={selectedPlan}
      />
    </ProviderDashboardLayout>
  );
};

export default ProviderPlans;
