import { PlanManagementForm } from "@/components/admin/provider-tier/PlanManagementForm";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useState } from "react";
import { ProviderPlan } from "@/components/admin/provider-tier/schemas";
import { Dialog, DialogContent } from "@/components/ui/dialog";

// Mock data for plans - in a real app, this would come from an API
const MOCK_PLANS: ProviderPlan[] = [
  {
    id: "free",
    name: "Starter",
    price: 0,
    description: "Get started with the basic tools you need",
    commission: "20%",
    features: [
      { included: true, text: "Access to bidding jobs" },
      { included: true, text: "Basic job alerts" },
      { included: true, text: "Standard visibility" },
      { included: true, text: "Basic dashboard analytics" },
      { included: true, text: "Community support" },
      { included: false, text: "Priority access to leads" },
      { included: false, text: "Advanced insights dashboard" },
      { included: false, text: "BNPL (Buy Now Pay Later)" },
    ],
  },
  {
    id: "pro",
    name: "Pro",
    price: 159,
    description: "Grow your business with more leads & tools",
    commission: "15%",
    features: [
      { included: true, text: "Access to bidding jobs" },
      { included: true, text: "Priority access to leads" },
      { included: true, text: "Instant alerts" },
      { included: true, text: "Boosted visibility" },
      { included: true, text: "Advanced insights dashboard" },
      { included: true, text: "Add-on for BNPL" },
      { included: true, text: "Email support" },
      { included: false, text: "Premium featured placement" },
      { included: false, text: "Full business tools" },
    ],
  },
  {
    id: "elite",
    name: "Elite",
    price: 319,
    description: "Maximize your business with premium tools",
    commission: "12%",
    features: [
      { included: true, text: "Access to bidding jobs" },
      { included: true, text: "Top placement for leads" },
      { included: true, text: "Real-time priority alerts" },
      { included: true, text: "Premium featured placement" },
      { included: true, text: "Full business tools" },
      { included: true, text: "CRM and client management" },
      { included: true, text: "BNPL included" },
      { included: true, text: "Priority support" },
    ],
  },
];

const AdminProviderTierPage = () => {
  const [plans, setPlans] = useState<ProviderPlan[]>(MOCK_PLANS);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingPlan, setEditingPlan] = useState<ProviderPlan | undefined>(undefined);
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Provider Tier Management</h1>
          <p className="text-muted-foreground">
            Manage subscription plans and assign tiers to providers.
          </p>
        </div>
      </div>

      <div className="space-y-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Available Subscription Plans</h2>
            <Button
              onClick={() => {
                setEditingPlan(undefined);
                setIsDialogOpen(true);
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add New Plan
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {plans.map((plan) => (
              <Card key={plan.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>{plan.name}</CardTitle>
                      <CardDescription>{plan.description}</CardDescription>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold">
                        ${plan.price}{plan.price > 0 && <span className="text-sm font-normal">/mo</span>}
                      </div>
                      <div className="text-sm text-muted-foreground">{plan.commission} commission</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Features</h3>
                    <ul className="space-y-1 text-sm">
                      {plan.features.map((feature, index) => (
                        <li key={index} className={`flex items-start ${!feature.included ? 'text-muted-foreground' : ''}`}>
                          <span className={`mr-2 ${feature.included ? 'text-green-500' : 'text-muted-foreground'}`}>
                            {feature.included ? '✓' : '×'}
                          </span>
                          {feature.text}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="mt-4 flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setEditingPlan(plan);
                        setIsDialogOpen(true);
                      }}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => {
                        setPlans(plans.filter(p => p.id !== plan.id));
                      }}
                    >
                      Delete
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
      </div>

      {/* Plan Management Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl">
          <PlanManagementForm
            initialData={editingPlan}
            onSubmit={async (plan: ProviderPlan) => {
              if (editingPlan) {
                // Update existing plan
                setPlans(plans.map(p => p.id === plan.id ? plan : p));
              } else {
                // Add new plan
                setPlans([...plans, plan]);
              }
              setIsDialogOpen(false);
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminProviderTierPage;